import 'package:flutter/material.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/models/books/chapter_model.dart';
import 'package:nsl/models/ui/collection_data.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/services/books_service.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/flex_mapper.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:nsl/utils/logger.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:nsl/widgets/resizable_panel.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'web_transaction_execution.dart';

class WebCollectionWidgets extends StatefulWidget {
  final String? bookId;

  const WebCollectionWidgets({super.key, this.bookId});

  @override
  State<WebCollectionWidgets> createState() => _WebCollectionWidgetsState();
}

class _WebCollectionWidgetsState extends State<WebCollectionWidgets> {
  // Dynamic tree data from API using ChapterModel
  List<ChapterModel> chapters = [];

  // Loading and error states
  bool _isLoading = true;
  String? _errorMessage;

  // Books service for API calls
  final BooksService _booksService = BooksService();

  // Widget to display in the content area
  Widget? _contentWidget;

  // Collection data for the back navigation - will be updated with actual book name
  final CollectionData _collectionData = CollectionData.loading();

  // Flag to show collection controls (toggle and expand)
  bool _showCollectionControls = false;

  // Flag to track expanded/fullscreen mode
  bool _isExpanded = false;

  // Flag to track if chat is enabled
  bool _isChatEnabled = false;

  // Resizable panel state
  double _treePanelWidth = 250.0; // Default width for tree navigation
  final double _minTreePanelWidth = 200.0; // Minimum width
  final double _maxTreePanelWidth = 350.0; // Maximum width

  @override
  void initState() {
    super.initState();
    _loadChapterData();
  }

  /// Load chapter data from API
  Future<void> _loadChapterData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Get book ID from widget parameter or SharedPreferences
      String? bookId = widget.bookId;
      String? bookName;

      if (bookId == null) {
        final prefs = await SharedPreferences.getInstance();
        bookId = prefs.getString('selected_book_id');
        bookName = prefs.getString('selected_book_name');
      }

      if (bookId == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'No book ID provided';
        });
        return;
      }

      // Set book name immediately if available from SharedPreferences
      if (bookName != null) {
        setState(() {
          _collectionData.updateName(bookName!);
        });
      }

      Logger.info('Loading chapters for book: $bookId');

      final chapters = await _booksService.getBookChaptersWithUserTenant(
        bookId: bookId,
      );

      Logger.info('Loaded ${chapters.length} chapters');
      Logger.info('Chapters data: ${chapters.map((c) => c.toJson()).toList()}');

      setState(() {
        this.chapters = chapters;
        _isLoading = false;

        // Update collection data with the book name from SharedPreferences (if not already set)
        if (bookName != null) {
          _collectionData.updateName(bookName!);
        } else if (_collectionData.isLoading) {
          _collectionData.updateName('Unknown Book');
        }
      });
    } catch (e) {
      Logger.error('Error loading chapter data: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load chapters: ${e.toString()}';

        // Set fallback book name if still loading
        if (_collectionData.isLoading) {
          _collectionData.updateName('Unknown Book');
        }
      });
    }
  }

  /// Load objectives for a specific chapter using the new API
  Future<void> _loadChapterObjectives(ChapterModel chapter) async {
    final chapterId = chapter.chapterId;
    if (chapterId == null) {
      Logger.error('No chapter ID found for loading objectives');
      return;
    }

    try {
      setState(() {
        chapter.isLoading = true;
        chapter.errorMessage = null;
      });

      Logger.info('Loading objectives for chapter: $chapterId using new API');

      // Use the new getAllObjectives method
      final allObjectives = await _booksService.getAllObjectives();

      // Filter objectives by chapter ID
      final chapterObjectives = allObjectives.where((objective) {
        // Handle both string and integer chapter IDs
        final objectiveChapterId = objective.chapterId?.toString();
        return objectiveChapterId == chapterId;
      }).toList();

      Logger.info(
          'Loaded ${chapterObjectives.length} objectives for chapter: $chapterId (from ${allObjectives.length} total objectives)');
      
      // Debug: Log all chapter IDs from objectives
      Logger.info('Available chapter IDs in objectives: ${allObjectives.map((obj) => obj.chapterId).toSet().toList()}');
      Logger.info('Looking for chapter ID: $chapterId');

      // Convert objectives to tree data format
      final List<Map<String, dynamic>> objectiveChildren =
          chapterObjectives.map((objective) {
        return {
          "name": objective.name ?? 'Unknown Objective',
          "isExpanded": false,
          "isSelected": false,
          "children": [],
          "image": "assets/images/my_business/box.svg",
          "dropdownImage": "assets/images/my_business/dropdown_collection.svg",
          "objectiveId": objective.goId,
          "chapterId": objective.chapterId,
          "bookId": objective.bookId,
          "type": "objective", // Mark as objective type
        };
      }).toList();

      setState(() {
        // Update the chapter with loaded objectives
        chapter.children = objectiveChildren;
        chapter.isExpanded = true; // Expand to show the loaded children
        chapter.isLoading = false;
      });
    } catch (e) {
      Logger.error('Error loading objectives for chapter $chapterId: $e');
      setState(() {
        chapter.isLoading = false;
        chapter.errorMessage = 'Failed to load objectives: ${e.toString()}';
      });
    }
  }

  /// Handle objective selection - update content widget to show WebTransactionExecution
  void _handleObjectiveSelection(String objectiveId, String objectiveName) {
    Logger.info('Objective selected: $objectiveId - $objectiveName');

    setState(() {
      _contentWidget = WebTransactionExecution(
          key: ValueKey(objectiveId),
          objectiveId: objectiveId,
          objectiveName: objectiveName,
          isLeftSideExpanded: _isExpanded,
          onSuccess: () {
            _contentWidget = null;
            setState(() {});
          });
    });
  }

  @override
  Widget build(BuildContext context) {
    return NSLKnowledgeLoaderWrapper(
        isLoading: _isLoading,
        child: Scaffold(
          backgroundColor: Color(0xffF7F9FB),
          body: SafeArea(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Tree structure with resizable panel - only visible when not expanded
                if (!_isExpanded) ...[
                  ResizablePanel(
                    width: _treePanelWidth,
                    minWidth: _minTreePanelWidth,
                    maxWidth: _maxTreePanelWidth,
                    handlePosition: ResizeHandlePosition.right,
                    onResize: (newWidth) {
                      setState(() {
                        _treePanelWidth = newWidth;
                      });
                    },
                    child: Container(
                      height: MediaQuery.of(context)
                          .size
                          .height, // Take full height
                      color: Color(0xffF7F9FB),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Back navigation element (outside the box)
                          _buildBackNavigation(),
                          SizedBox(
                              height:
                                  8), // Add some space between back navigation and tree box

                          // Expanded tree container to take remaining height
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                color: Color(0xffF7F9FB),
                                border: Border(
                                  top: BorderSide(
                                      color: Color(0xffD0D0D0), width: 1.0),
                                  bottom: BorderSide(
                                      color: Color(0xffD0D0D0), width: 1.0),
                                ),
                                borderRadius: BorderRadius.circular(2),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Show error or tree data (loading handled by wrapper)
                                  if (_errorMessage != null)
                                    Container(
                                      padding: EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'Error loading chapters',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.red,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            _errorMessage!,
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                          SizedBox(height: 8),
                                          GestureDetector(
                                            onTap: _loadChapterData,
                                            child: Text(
                                              'Retry',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Color(0xff0058FF),
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  else if (chapters.isEmpty)
                                    Container(
                                      padding: EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'No chapters found',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            'This book currently has no chapters available.',
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: Colors.grey[500],
                                            ),
                                          ),
                                          SizedBox(height: 8),
                                          GestureDetector(
                                            onTap: _loadChapterData,
                                            child: Text(
                                              'Refresh',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Color(0xff0058FF),
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  else
                                    // Scrollable tree content
                                    Expanded(
                                      child: SingleChildScrollView(
                                        child: Column(
                                          children: chapters
                                              .asMap()
                                              .entries
                                              .map((entry) {
                                            final index = entry.key;
                                            final chapter = entry.value;
                                            return Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                _buildChapterTreeItem(
                                                    chapter, 0),
                                                // Add divider after each item except the last one
                                                if (index < chapters.length - 1)
                                                  Divider(
                                                      height: 1,
                                                      color: Color(0xffD0D0D0)),
                                              ],
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Vertical divider between tree and main content
                  Container(
                    width: 1,
                    height: MediaQuery.of(context).size.height,
                    color: Color(0xffD0D0D0),
                  ),
                ],
                // Main content area
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Fixed header row containing collection controls
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Collection controls (toggle and expand buttons) - positioned at the same level as back navigation
                          // if (_showCollectionControls)
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: EdgeInsets.only(top: AppSpacing.sm),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      height: 20,
                                      width: 25,
                                      child: Transform.scale(
                                        scale: 0.6,
                                        child: Switch.adaptive(
                                          value: _isChatEnabled,
                                          activeColor: Colors.white,
                                          activeTrackColor: Color(0xff0058FF),
                                          inactiveThumbColor: Colors.white,
                                          inactiveTrackColor: Colors.grey[300],
                                          trackOutlineColor:
                                              WidgetStateProperty.all(
                                                  Colors.transparent),
                                          materialTapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                          onChanged: (value) {
                                            setState(() {
                                              _isChatEnabled = value;
                                            });
                                            // Note: Chat toggle functionality can be implemented here if needed
                                          },
                                        ),
                                      ),
                                    ),
                                    // SizedBox(
                                    //     height:
                                    //         4), // Spacing between toggle and label
                                    Text(
                                      _isChatEnabled ? "NSL" : "Normal",
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.black,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                  width: AppSpacing
                                      .xxs), // Smaller gap between buttons
                              // Expand Collection button - Fullscreen
                              Container(
                                width: 32,
                                height: 32,
                                padding: EdgeInsets.only(top: AppSpacing.sm),
                                decoration: BoxDecoration(
                                  color: Color(
                                      0xFFF7F9FB), // Light gray background
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () {
                                      // Toggle expanded/fullscreen mode
                                      setState(() {
                                        _isExpanded = !_isExpanded;

                                        // Change the number of widgets per row based on expanded state
                                        if (_isExpanded) {
                                          // In expanded mode, show 5 widgets per row
                                          FlexMapper.setTotalRowFlex(5);
                                        } else {
                                          // In normal mode, show 4 widgets per row
                                          FlexMapper.setTotalRowFlex(4);
                                        }
                                      });
                                    },
                                    child: Center(
                                      child: CustomImage.asset(
                                        // Use expand icon
                                        'assets/images/my_business/expand_collection.svg',
                                        width: 18,
                                        height: 18,
                                      ).toWidget(),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),

                      // Content widget
                      Expanded(
                        child: Container(
                          color: Color(0xffF7F9FB),
                          child: _contentWidget ??
                              Center(
                                child: Text(
                                    'Select an item from the tree navigation'),
                              ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  // Build back navigation element with arrow and text
  Widget _buildBackNavigation() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // Handle back navigation
          // You can add navigation logic here
          Provider.of<WebHomeProvider>(context, listen: false)
              .currentScreenIndex = ScreenConstants.myBusinessCollections;
        },
        child: Center(
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.only(
              left: AppSpacing.lg,
              // right: AppSpacing.sm,
              top: AppSpacing.sm,
              bottom: AppSpacing.xxs,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Back arrow icon
                CustomImage.asset(
                  _collectionData.backImage,
                  width: 12,
                  height: 12,
                  color: Colors.black,
                ).toWidget(),
                SizedBox(width: AppSpacing.sm),

                // Collection name text
                Expanded(
                  child: Text(
                    _collectionData.name,
                    style: TextStyle(
                      // fontFamily: 'TiemposText',
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Function to build chapter tree items
  Widget _buildChapterTreeItem(ChapterModel chapter, int level) {
    return _ChapterTreeItem(
      chapter: chapter,
      level: level,
      onToggle: () async {
        setState(() {
          chapter.isExpanded = !chapter.isExpanded;
        });

        // If expanding a chapter and it doesn't have children loaded yet, load objectives
        if (chapter.isExpanded && chapter.children.isEmpty) {
          await _loadChapterObjectives(chapter);
        }
      },
      onSelect: (selectedChapter) async {
        setState(() {
          // Reset all chapters' selection state
          for (var ch in chapters) {
            ch.isSelected = false;
          }

          // Set the selected chapter
          selectedChapter.isSelected = true;

          // Reset collection controls flag
          _showCollectionControls = false;

          // // Set the content widget to show selected chapter info
          // _contentWidget = Container(
          //   padding: EdgeInsets.all(16),
          //   child: Column(
          //     mainAxisAlignment: MainAxisAlignment.center,
          //     children: [
          //       Text(
          //         'Selected: ${selectedChapter.chapterName ?? 'Unknown Chapter'}',
          //         style: TextStyle(
          //           fontFamily: 'TiemposText',
          //           fontSize: 14,
          //           fontWeight: FontWeight.w500,
          //         ),
          //       ),
          //       SizedBox(height: 16),
          //       Text(
          //         'Objectives: ${selectedChapter.objectiveCount ?? 0}',
          //         style: TextStyle(
          //           fontSize: 12,
          //           color: Colors.grey[600],
          //         ),
          //       ),
          //     ],
          //   ),
          // );
        });
      },
      onObjectiveSelected: _handleObjectiveSelection,
    );
  }
}

// Separate stateful widget for chapter tree items to handle hover state properly
class _ChapterTreeItem extends StatefulWidget {
  final ChapterModel chapter;
  final int level;
  final VoidCallback onToggle;
  final Function(ChapterModel) onSelect;
  final Function(String, String) onObjectiveSelected;

  const _ChapterTreeItem({
    required this.chapter,
    required this.level,
    required this.onToggle,
    required this.onSelect,
    required this.onObjectiveSelected,
  });

  @override
  State<_ChapterTreeItem> createState() => _ChapterTreeItemState();
}

class _ChapterTreeItemState extends State<_ChapterTreeItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MouseRegion(
          onEnter: (_) => setState(() => isHovered = true),
          onExit: (_) => setState(() => isHovered = false),
          child: InkWell(
            onTap: () {
              // Toggle expansion
              widget.onToggle();

              // Select this chapter
              widget.onSelect(widget.chapter);
            },
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: 12.0,
                vertical: 10.0,
              ),
              decoration: BoxDecoration(
                // border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
                color: widget.chapter.isSelected
                    ? Color(
                        0xffE6F0FF) // Light blue background for selected item
                    : isHovered
                        ? Color(0xffF0F0F0)
                        : Colors.white,
              ),
              child: Row(
                children: [
                  // Chapter icon
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CustomImage.asset(
                      'assets/images/my_business/box_add.svg',
                      width: 12,
                      height: 12,
                      color: isHovered ? Color(0xff0058FF) : Colors.grey[700],
                    ).toWidget(),
                  ),
                  SizedBox(width: 8),

                  // Chapter name with loading indicator
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.chapter.chapterName ?? 'Unknown Chapter',
                            style: TextStyle(
                              fontFamily: 'TiemposText',
                              fontSize: 12,
                              fontWeight: widget.chapter.isSelected
                                  ? FontWeight.bold
                                  : FontWeight.w500,
                              color:
                                  isHovered ? Color(0xff0058FF) : Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // Show loading indicator if this chapter is loading objectives
                        if (widget.chapter.isLoading)
                          Container(
                            margin: EdgeInsets.only(left: 8),
                            child: SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(
                                strokeWidth: 1.5,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    Color(0xff0058FF)),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Show dropdown arrow
                  Transform.rotate(
                    angle: widget.chapter.isExpanded
                        ? 3.14159
                        : 0, // 180 degrees when expanded
                    child: CustomImage.asset(
                      'assets/images/my_business/dropdown_collection.svg',
                      width: 20,
                      height: 20,
                    ).toWidget(),
                  )
                ],
              ),
            ),
          ),
        ),

        // Show objectives if expanded
        if (widget.chapter.isExpanded && widget.chapter.children.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(left: 18.0), // Indent child items
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widget.chapter.children
                  .map<Widget>((child) => _ObjectiveTreeItem(
                        objective: child,
                        level: widget.level + 1,
                        onObjectiveSelected: widget.onObjectiveSelected,
                      ))
                  .toList(),
            ),
          ),

        // Show error message if objectives failed to load
        if (widget.chapter.isExpanded && widget.chapter.errorMessage != null)
          Padding(
            padding: const EdgeInsets.only(left: 24.0, top: 8.0),
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red[50],
                border: Border.all(color: Colors.red[200]!),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Error loading objectives',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.red[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    widget.chapter.errorMessage!,
                    style: TextStyle(
                      fontSize: 9,
                      color: Colors.red[600],
                    ),
                  ),
                  SizedBox(height: 4),
                  GestureDetector(
                    onTap: () {
                      // Retry loading objectives
                      widget.onToggle();
                    },
                    child: Text(
                      'Retry',
                      style: TextStyle(
                        fontSize: 10,
                        color: Color(0xff0058FF),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}

// Clickable widget for displaying objectives
class _ObjectiveTreeItem extends StatefulWidget {
  final Map<String, dynamic> objective;
  final int level;
  final Function(String, String) onObjectiveSelected;

  const _ObjectiveTreeItem({
    required this.objective,
    required this.level,
    required this.onObjectiveSelected,
  });

  @override
  State<_ObjectiveTreeItem> createState() => _ObjectiveTreeItemState();
}

class _ObjectiveTreeItemState extends State<_ObjectiveTreeItem> {
  bool isHovered = false;
  bool isSelected = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: InkWell(
        onTap: () {
          final objectiveId = widget.objective['objectiveId'] as String?;
          final objectiveName = widget.objective['name'] as String?;

          if (objectiveId != null && objectiveName != null) {
            isSelected = true;
            widget.onObjectiveSelected(objectiveId, objectiveName);
          }
        },
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          decoration: BoxDecoration(
            // border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
            color: // light gray on hover
                Colors.transparent,
          ),
          child: Row(
            children: [
              // Objective icon
              SizedBox(
                width: 12,
                height: 12,
                child: CustomImage.asset(
                  'assets/images/my_business/box.svg',
                  width: 12,
                  height: 12,
                  color: isHovered ? Color(0xff0058FF) : Colors.grey[600],
                ).toWidget(),
              ),
              SizedBox(width: 8),

              // Objective name
              Expanded(
                child: Text(
                  widget.objective['name'] ?? 'Unknown Objective',
                  style: TextStyle(
                    fontFamily: 'TiemposText',
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                    color: isHovered ? Color(0xff0058FF) : Colors.grey[700],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// class _ObjectiveTreeItemState extends State<_ObjectiveTreeItem> {
//   bool isHovered = false;

//   @override
//   Widget build(BuildContext context) {
//     return MouseRegion(
//       onEnter: (_) => setState(() => isHovered = true),
//       onExit: (_) => setState(() => isHovered = false),
//       child: InkWell(
//         onTap: () {
//           // Handle objective click
//           final objectiveId = widget.objective['objectiveId'] as String?;
//           final objectiveName = widget.objective['name'] as String?;

//           if (objectiveId != null && objectiveName != null) {
//             widget.onObjectiveSelected(objectiveId, objectiveName);
//           }
//         },
//         hoverColor: Colors.transparent,
//         splashColor: Colors.transparent,
//         highlightColor: Colors.transparent,
//         child: Container(
//           width: double.infinity,
//           padding: EdgeInsets.symmetric(
//             horizontal: 12.0,
//             vertical: 8.0,
//           ),
//           decoration: BoxDecoration(
//             // color: isHovered ? Color(0xffF0F0F0) : Colors.transparent,
//             color: Colors.transparent,
//           ),
//           child: Row(
//             children: [
//               // Objective icon
//               SizedBox(
//                 width: 12,
//                 height: 12,
//                 child: CustomImage.asset(
//                   'assets/images/my_business/box.svg',
//                   width: 12,
//                   height: 12,
//                   color: isHovered ? Color(0xff0058FF) : Colors.grey[600],
//                 ).toWidget(),
//               ),
//               SizedBox(width: 8),

//               // Objective name
//               Expanded(
//                 child: Text(
//                   widget.objective['name'] ?? 'Unknown Objective',
//                   style: TextStyle(
//                     fontFamily: 'TiemposText',
//                     fontSize: 11,
//                     fontWeight: FontWeight.w400,
//                     color: isHovered ? Color(0xff0058FF) : Colors.grey[700],
//                   ),
//                   overflow: TextOverflow.ellipsis,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
