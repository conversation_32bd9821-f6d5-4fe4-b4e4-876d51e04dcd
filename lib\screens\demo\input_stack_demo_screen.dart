import 'package:flutter/material.dart';
import 'package:nsl/screens/web/static_flow/widgets/lo_input_stack_accordion.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';

/// Demo screen to showcase the Input Stack Accordion component
class InputStackDemoScreen extends StatefulWidget {
  const InputStackDemoScreen({super.key});

  @override
  State<InputStackDemoScreen> createState() => _InputStackDemoScreenState();
}

class _InputStackDemoScreenState extends State<InputStackDemoScreen> {
  final AccordionController _accordionController = AccordionController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Input Stack Accordion Demo'),
        backgroundColor: const Color(0xFF0058FF),
        foregroundColor: Colors.white,
      ),
      body: Container(
        color: const Color(0xFFF5F5F5),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Input Stack Accordion Component',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'This component demonstrates an accordion-style interface for managing input stack attributes with dropdown functionality, helper text, error messages, and fixed action buttons.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 32),

              // Input Stack Accordion Component
              LoInputStackAccordion(
                accordionController: _accordionController,
                title: 'Inputs Stack',
                attributeCount: 25,
                availableAttributes: const [
                  '25 Attributes',
                  '20 Attributes',
                  '15 Attributes',
                  '10 Attributes',
                  '5 Attributes',
                ],
                isExpanded: true, // Start expanded for demo
                onAttributeSelected: (value) {
                  print('Attribute selected: $value');
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Selected: $value'),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
                onMyLibraryPressed: () {
                  print('My Library pressed');
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('My Library button pressed'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                onExpansionChanged: (isExpanded) {
                  print('Expansion changed: $isExpanded');
                },
              ),

              const SizedBox(height: 32),

              // Feature highlights
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFE5E7EB)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Features Implemented:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildFeatureItem(
                        '✅ Accordion expansion/collapse functionality'),
                    _buildFeatureItem(
                        '✅ Custom dropdown widgets for Data Source and UI Control columns'),
                    _buildFeatureItem(
                        '✅ Clickable links in Function Type column'),
                    _buildFeatureItem(
                        '✅ Status indicators with proper styling'),
                    _buildFeatureItem(
                        '✅ Helper text and error message columns'),
                    _buildFeatureItem(
                        '✅ Fixed/sticky Actions column with delete buttons'),
                    _buildFeatureItem('✅ Horizontal scrollable table content'),
                    _buildFeatureItem('✅ My Library button with add icon'),
                    _buildFeatureItem('✅ Attributes dropdown in header'),
                    _buildFeatureItem('✅ CP toggle switch'),
                    _buildFeatureItem(
                        '✅ Responsive design with proper spacing'),
                    _buildFeatureItem('✅ Consistent theming and typography'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
        ),
      ),
    );
  }
}
