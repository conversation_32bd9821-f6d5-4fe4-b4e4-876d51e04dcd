import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:nsl/providers/my_library_provider.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/static_flow/extract_lo_details.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:provider/provider.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractGoDetailsMiddleStatic extends StatefulWidget {
  final String? sessionId; // New session-based API support
  final String? userIntent;

  const ExtractGoDetailsMiddleStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<ExtractGoDetailsMiddleStatic> createState() =>
      _ExtractGoDetailsMiddleStaticState();
}

class _ExtractGoDetailsMiddleStaticState
    extends State<ExtractGoDetailsMiddleStatic> {

  Map<String, String?> _selectedConditions = {};


  @override
  void initState() {
    super.initState();
    // Initialize library provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final libraryProvider =
          Provider.of<MyLibraryProvider>(context, listen: false);
      libraryProvider.fetchLibraryData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer4<WebHomeProviderStatic, ObjectCreationProvider,
        RolesProvider, GoDetailsProvider>(
      builder: (context, provider, objectCreationProvider, rolesProvider,
          goDetailsProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(
                    context, provider, rolesProvider, goDetailsProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000), // Black with 10% opacity
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),

              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // ai label on the right
        Text(
          'Form',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 12),
        // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 100,
              maxHeight: MediaQuery.of(context).size.height,
            ),
            child: _buildContentWithLineNumbers(
                context, rolesProvider, goDetailsProvider),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWithLineNumbers(BuildContext context,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];
    if (goDetailsProvider.showLocalObjectiveDetails) {
      return ExtractLoDetailsMiddleStatic();
    }

    // Line 1: Solution row
    allWidgets
        .add(_buildLineWithNumber(lineNumber++, Consumer<MyLibraryProvider>(
      builder: (context, libraryProvider, child) {
        return _buildFirstRow(context, libraryProvider, goDetailsProvider);
      },
    )));
    allWidgets.add(const SizedBox(height: 16));

    // Line 2: Description row
    allWidgets.add(_buildLineWithNumber(
        lineNumber++, _buildSecondRow(context, goDetailsProvider)));

    // Show local objectives section after validation
    if (goDetailsProvider.currentStep == GoDetailsStep.afterValidation ||
        goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
      allWidgets.add(const SizedBox(height: 24));

      // Line 3: LOCAL OBJECTIVES header
      allWidgets.add(
          _buildLineWithNumber(lineNumber++, _buildLocalObjectivesHeader()));
      allWidgets.add(const SizedBox(height: 8));

      // Local objectives content with line numbers
      if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
        // Show LO list with line numbers
        for (final entry in goDetailsProvider.localObjectives.asMap().entries) {
          final index = entry.key;
          final objective = entry.value;

          // Add spacing before each LO (except the first one)
          if (index > 0) {
            allWidgets.add(const SizedBox(height: 12));
          }

          // Add LO item
          allWidgets.add(_buildLineWithNumber(
            lineNumber++,
            _buildLocalObjectiveItem(index, objective, goDetailsProvider),
          ));

          // Add LO insertion text field if open for this LO
          if (goDetailsProvider.isLoInsertionOpen(index)) {
            allWidgets.add(_buildLineWithNumber(
              lineNumber++,
              _buildLoInsertionField(index, goDetailsProvider),
            ));
          }

          // Add pathway creation fields if open for this LO
          if (goDetailsProvider.isPathwayCreationOpen(index)) {
            final selectedType =
                goDetailsProvider.getPathwaySelectedType(index);

            if (selectedType == 'Alternative' || selectedType == 'Parallel') {
              // Add Apply Condition section with individual line numbers
              List<Widget> conditionWidgets =
                  _buildApplyConditionFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber);

              allWidgets.add(
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: IntrinsicWidth(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: conditionWidgets,
                    ),
                  ),
                ),
              );
              lineNumber +=
                  4; // 4 rows: header + first condition + second condition + +LO
            } else if (selectedType == 'Sequential' ||
                selectedType == 'Recursive' ||
                selectedType == 'Terminal') {
              // Add blue container with individual line numbers for Sequential, Recursive, Terminal
              allWidgets.addAll(
                  _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for these types
            } else {
              // For initial state (no type selected) or any other types - wrap in blue container
              allWidgets.addAll(_buildInitialPathwayFieldsWithLineNumbers(
                  index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for initial fields
            }
          }
        }
      } else {
        // Line 4: Input field
        allWidgets.add(_buildLineWithNumber(lineNumber++,
            _buildLocalObjectiveInput(context, goDetailsProvider)));
      }
    }

    return Stack(
      children: [
        // Continuous vertical line
        Positioned(
          left: 28, // Position after line number (20px width + 8px margin)
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            color: Colors.grey.shade300,
          ),
        ),
        // Content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: allWidgets,
        ),
      ],
    );
  }

  // Build Apply Condition fields with individual line numbers outside blue container
  List<Widget> _buildApplyConditionFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);
    final dropdownAttributeWidth = _getAttributeDropdownWidth(context);


    // Row 1: Apply Condition header with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 16),
            const Text(
              'Apply Condition',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ],
        ),
      ),
    ));

    // Row 2: First condition row with pathway fields and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            // Select Role dropdown
            SizedBox(
              width: dropdownWidth,
              child: Consumer<MyLibraryProvider>(
                builder: (context, libraryProvider, child) {
                  return _buildPathwayRoleField(
                      loIndex, goDetailsProvider, libraryProvider);
                },
              ),
            ),
            const SizedBox(width: 8),

            // Select Type dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildPathwayTypeField(loIndex, goDetailsProvider),
            ),
            const SizedBox(width: 8),

            // Select LO dropdown (First LO for alternative/parallel)
            SizedBox(
              width: dropdownWidth,
              child: _buildFirstLODropdown(loIndex, goDetailsProvider),
            ),
            const SizedBox(width: 16),

            // Entity Attribute dropdown (First)
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(
                loIndex: loIndex,
                isFirst: true,
                isAfterCondition: false,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
            const SizedBox(width: 8),

            // Condition dropdown (First)
            SizedBox(
              width: dropdownWidth,
              child: _buildConditionDropdown(
                loIndex: loIndex,
                isFirst: true,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
            const SizedBox(width: 8),

            // Entity Attribute dropdown (First, after condition)
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(
                loIndex: loIndex,
                isFirst: true,
                isAfterCondition: true,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
          ],
        ),
      ),
    ));

    // Row 3: Second condition row with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            // Empty space to align with dropdowns above
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),

            // Second LO dropdown
            SizedBox(
              width: dropdownWidth,
              child: _buildSecondLODropdown(loIndex, goDetailsProvider),
            ),
            const SizedBox(width: 16),

            // Entity Attribute dropdown (Second)
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(
                loIndex: loIndex,
                isFirst: false,
                isAfterCondition: false,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
            const SizedBox(width: 8),

            // Condition dropdown (Second)
            SizedBox(
              width: dropdownWidth,
              child: _buildConditionDropdown(
                loIndex: loIndex,
                isFirst: false,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
            const SizedBox(width: 8),

            // Entity Attribute dropdown (Second, after condition)
            SizedBox(
              width: dropdownAttributeWidth,
              child: _buildEntityAttributeDropdown(
                loIndex: loIndex,
                isFirst: false,
                isAfterCondition: true,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
          ],
        ),
      ),
    ));

    // Row 4: + LO button with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7), // Light blue background
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(4),
            bottomRight: Radius.circular(4),
          ),
        ),
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            SizedBox(width: dropdownWidth),
            const SizedBox(width: 8),
            const Text(
              '+ LO',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.blue,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ],
        ),
      ),
    ));

    return widgets;
  }

  // Build initial pathway fields (Role and Type dropdowns) with blue container and line numbers
  List<Widget> _buildInitialPathwayFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color:
              Color(0xFFF7F7F7), // Blue background (same as other containers)
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        padding: const EdgeInsets.all(12),
        child: _buildInitialPathwayContent(
            loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
      ),
    ));

    return widgets;
  }

  // Build content for initial pathway creation (Role and Type dropdowns only)
  Widget _buildInitialPathwayContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildPathwayRoleField(
                  loIndex, goDetailsProvider, libraryProvider);
            },
          ),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),
        // No third field for initial state
      ],
    );
  }

  // Build Sequential, Recursive, Terminal fields with individual line numbers and blue container
  List<Widget> _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7),
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        padding: const EdgeInsets.all(12),
        child: _buildSequentialRecursiveTerminalContent(
            loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
      ),
    ));

    return widgets;
  }

  // Build content for Sequential, Recursive, Terminal types
  Widget _buildSequentialRecursiveTerminalContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);

    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildPathwayRoleField(
                  loIndex, goDetailsProvider, libraryProvider);
            },
          ),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),

        // Third field based on selected type
        if (selectedType == 'Sequential') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Recursive') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildRecursiveInputField(loIndex, goDetailsProvider),
          ),
        ],
        // Terminal type has no third field
      ],
    );
  }

  Widget _buildFirstRow(BuildContext context, MyLibraryProvider libraryProvider,
      GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        // Solution label and text field
        Expanded(
          flex: 3,
          child: Row(
            children: [
              const Text(
                'Solution:',
                style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              const SizedBox(width: AppSpacing.xs),
              Expanded(
                child: Container(
                  height: 24,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TextField(
                    controller: goDetailsProvider.solutionController,
                    decoration: const InputDecoration(
                      hintText: 'Type here..',
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      fillColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 4, vertical: 7), // Remove default padding
                      isDense: true,
                      //  contentPadding: EdgeInsets.symmetric(horizontal: 12,),
                      // hintText: 'Enter solution name',
                    ),
                    style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.xs),
              // Tick icon button
              InkWell(
                onTap: goDetailsProvider.isValidating
                    ? null
                    : () async {
                        // Create GoModel with collected data before validation
                        final goModel =
                            _createGoModelFromControllers(goDetailsProvider);

                        // Pass the GoModel to validation
                        await goDetailsProvider
                            .validateSolutionWithGoModel(goModel);
                      },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: const Color(0xFF007AFF),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 24),

        // Agent Type dropdown
        Expanded(
          flex: 1,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildAgentTypeDropdown(
                  context, libraryProvider, goDetailsProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAgentTypeDropdown(BuildContext context,
      MyLibraryProvider libraryProvider, GoDetailsProvider goDetailsProvider) {
    // Fetch library data if not already loaded
    if (!libraryProvider.isLoading && libraryProvider.roles.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        libraryProvider.fetchLibraryData();
      });
    }

    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 24,
            child: CustomDropdownWidget(
              label: 'Role Type',
              list: libraryProvider.isLoading
                  ? ['Loading roles...']
                  : libraryProvider.roles.isEmpty
                      ? ['No roles available']
                      : libraryProvider.getRoleNames(),
              value: libraryProvider.selectedRole?.name,
              onChanged: (value) {
                if (!libraryProvider.isLoading &&
                    value != null &&
                    value != 'Loading roles...' &&
                    value != 'No roles available') {
                  libraryProvider.setSelectedRoleByName(value);

                  // Convert RolesPostgre to PostgresRole for compatibility with GoDetailsProvider
                  final selectedLibraryRole =
                      libraryProvider.getRoleByName(value);
                  if (selectedLibraryRole != null) {
                    // Create a PostgresRole object from RolesPostgre
                    final postgresRole = PostgresRole(
                      roleId: selectedLibraryRole.roleId,
                      name: selectedLibraryRole.name,
                      description: selectedLibraryRole.description,
                      tenantId: null, // Skip tenantId due to type mismatch
                      createdAt: selectedLibraryRole.createdAt,
                      updatedAt: selectedLibraryRole.updatedAt,
                    );
                    goDetailsProvider.setSelectedRole(postgresRole);
                  }
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondRow(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        const Text(
          'Description:',
          style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText),
        ),
        const SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Container(
            height: 24,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: TextField(
              controller: goDetailsProvider.descriptionController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                isDense: true,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding: EdgeInsets.symmetric(
                    horizontal: 4, vertical: 7), // Remove default padding

                hintText: 'Enter description..',
              ),
              style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
          ),
        ),
      ],
    );
  }

   // Helper methods for line numbers
  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Line number
        Container(
          // color: Color(0xFFEDF3FF),
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildLocalObjectivesHeader() {
    return const Text(
      'LOCAL OBJECTIVES',
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w700,
        color: Colors.black,
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
    );
  }

  Widget _buildLocalObjectiveItem(
      int index, String objective, GoDetailsProvider goDetailsProvider) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // LO number and text
        InkWell(
          onTap: () {
            // Show local objective details within the same screen
            goDetailsProvider.setShowLocalObjectiveDetails(index);
          },
          child: Text(
            'LO-${index + 1}. $objective',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        // Create pathway button
        InkWell(
          onTap: () {
            goDetailsProvider.togglePathwayCreation(index);
          },
          child: const Text(
            'Create pathway',
            style: TextStyle(
              decoration: TextDecoration.underline,
              decorationColor: Colors.blue,
              fontSize: 10,
              fontWeight: FontWeight.w500,
              height: 2.0,
              color: Colors.blue,
            ),
          ),
        ),

        Spacer(),
        InkWell(
          onTap: () {
            goDetailsProvider.toggleLoInsertion(index);
          },
          child: const Icon(
            Icons.add,
            color: Colors.black,
            size: 18,
          ),
        ),
      ],
    );
  }

  Widget _buildLocalObjectiveInput(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 24,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
            ),
            child: TextField(
              controller: goDetailsProvider.localObjectiveController,
              decoration: const InputDecoration(
                border: InputBorder.none,
                isDense: true,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                fillColor: Colors.transparent,
                hoverColor: Colors.transparent,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 4, vertical: 7),
                hintText: 'Type LO name with full stop (.)',
                hintStyle: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
              style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Tick icon for local objective
        InkWell(
          onTap: () {
            goDetailsProvider.processLocalObjectives();
          },
          child: Container(
            width: 24,
            height: 24,
            decoration: const BoxDecoration(
              color: Color(0xFF007AFF),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoInsertionField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final controller = goDetailsProvider.getLoInsertionController(loIndex);
    if (controller == null) return const SizedBox.shrink();

    return Column(
      children: [
        SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 24,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: controller,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 7),
                    hintText: 'Type LO names with full stop (.)',
                    hintStyle: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                  style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Add icon for inserting LOs
            InkWell(
              onTap: () {
                goDetailsProvider.processLoInsertion(loIndex);
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Color(0xFF007AFF),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method to get responsive dropdown width
  double _getDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 100.0;
    } else {
      return (screenWidth * 100) / 1366; // Scale proportionally
    }
  }

  double _getAttributeDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 160.0;
    } else {
      return (screenWidth * 160) / 1366; // Scale proportionally
    }
  }

 

  // Helper method for Entity Attribute dropdown
  Widget _buildEntityAttributeDropdown({
    int? loIndex,
    bool isFirst = true,
    bool isAfterCondition = false,
    GoDetailsProvider? goDetailsProvider,
  }) {
    // Mock entity attributes - in real app this would come from API
    final entityAttributes = ['Name', 'Email', 'Phone', 'Address', 'Department'];

    String? currentValue;
    if (loIndex != null && goDetailsProvider != null) {
      if (isFirst) {
        currentValue = isAfterCondition
          ? goDetailsProvider.getPathwayFirstEntityAttributeAfterCondition(loIndex)
          : goDetailsProvider.getPathwayFirstEntityAttribute(loIndex);
      } else {
        currentValue = isAfterCondition
          ? goDetailsProvider.getPathwaySecondEntityAttributeAfterCondition(loIndex)
          : goDetailsProvider.getPathwaySecondEntityAttribute(loIndex);
      }
    }

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Entity Attribute',
        list: entityAttributes,
        value: currentValue,
        onChanged: (value) {
          if (loIndex != null && goDetailsProvider != null && value != null) {
            print('🔄 Entity Attribute Selection - LO: ${loIndex + 1}, Value: $value, IsFirst: $isFirst, IsAfterCondition: $isAfterCondition');
            if (isFirst) {
              if (isAfterCondition) {
                goDetailsProvider.setPathwayFirstEntityAttributeAfterCondition(loIndex, value);
                print('✅ Saved First Entity Attribute After Condition for LO-${loIndex + 1}: $value');
              } else {
                goDetailsProvider.setPathwayFirstEntityAttribute(loIndex, value);
                print('✅ Saved First Entity Attribute for LO-${loIndex + 1}: $value');
              }
            } else {
              if (isAfterCondition) {
                goDetailsProvider.setPathwaySecondEntityAttributeAfterCondition(loIndex, value);
                print('✅ Saved Second Entity Attribute After Condition for LO-${loIndex + 1}: $value');
              } else {
                goDetailsProvider.setPathwaySecondEntityAttribute(loIndex, value);
                print('✅ Saved Second Entity Attribute for LO-${loIndex + 1}: $value');
              }
            }
          } else {
            print('❌ Entity Attribute NOT saved - Missing parameters: loIndex=$loIndex, provider=${goDetailsProvider != null}, value=$value');
          }
        },
      ),
    );
  }

  // Helper method for Condition dropdown
  Widget _buildConditionDropdown({
    String? conditionKey,
    int? loIndex,
    bool isFirst = true,
    GoDetailsProvider? goDetailsProvider,
  }) {
    const List<String> conditions = [
      '==',
      '!=',
      '<',
      '<=',
      '>',
      '>=',
    ];

    // Generate a unique key if not provided
    final key =
        conditionKey ?? 'condition_${DateTime.now().millisecondsSinceEpoch}';

    String? currentValue;
    if (loIndex != null && goDetailsProvider != null) {
      currentValue = isFirst
        ? goDetailsProvider.getPathwayFirstCondition(loIndex)
        : goDetailsProvider.getPathwaySecondCondition(loIndex);
    } else {
      currentValue = _selectedConditions[key];
    }

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Condition',
        list: conditions,
        value: currentValue,
        onChanged: (value) {
          if (loIndex != null && goDetailsProvider != null && value != null) {
            print('🔄 Condition Selection - LO: ${loIndex + 1}, Value: $value, IsFirst: $isFirst');
            // Save to provider
            if (isFirst) {
              goDetailsProvider.setPathwayFirstCondition(loIndex, value);
              print('✅ Saved First Condition for LO-${loIndex + 1}: $value');
            } else {
              goDetailsProvider.setPathwaySecondCondition(loIndex, value);
              print('✅ Saved Second Condition for LO-${loIndex + 1}: $value');
            }
          } else {
            print('❌ Condition NOT saved - Missing parameters: loIndex=$loIndex, provider=${goDetailsProvider != null}, value=$value');
            // Fallback to local state
            setState(() {
              _selectedConditions[key] = value;
            });
          }
        },
      ),
    );
  }

  Widget _buildPathwayRoleField(int loIndex,
      GoDetailsProvider goDetailsProvider, MyLibraryProvider libraryProvider) {
    // Fetch library data if not already loaded
    if (!libraryProvider.isLoading && libraryProvider.roles.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        libraryProvider.fetchLibraryData();
      });
    }

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Role',
        list: libraryProvider.isLoading
            ? ['Loading roles...']
            : libraryProvider.roles.isEmpty
                ? ['No roles available']
                : libraryProvider.getRoleNames(),
        value: goDetailsProvider.getPathwaySelectedRole(loIndex)?.name,
        onChanged: (value) {
          if (!libraryProvider.isLoading &&
              value != null &&
              value != 'Loading roles...' &&
              value != 'No roles available') {
            // Convert RolesPostgre to PostgresRole for compatibility with GoDetailsProvider
            final selectedLibraryRole = libraryProvider.getRoleByName(value);
            if (selectedLibraryRole != null) {
              // Create a PostgresRole object from RolesPostgre
              final postgresRole = PostgresRole(
                roleId: selectedLibraryRole.roleId,
                name: selectedLibraryRole.name,
                description: selectedLibraryRole.description,
                tenantId: null, // Skip tenantId due to type mismatch
                createdAt: selectedLibraryRole.createdAt,
                updatedAt: selectedLibraryRole.updatedAt,
              );
              goDetailsProvider.setPathwaySelectedRole(loIndex, postgresRole);
            }
          }
        },
      ),
    );
  }

  Widget _buildPathwayTypeField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final typeOptions = [
      'Sequential',
      'Alternative',
      'Parallel',
      'Recursive',
      'Terminal'
    ];

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Type',
        list: typeOptions,
        value: goDetailsProvider.getPathwaySelectedType(loIndex),
        onChanged: (selectedType) {
          goDetailsProvider.setPathwaySelectedType(loIndex, selectedType);
        },
      ),
    );
  }

  Widget _buildPathwayLOField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: availableLOs.isEmpty ? ['No LOs available'] : availableLOs,
        value: goDetailsProvider.getPathwaySelectedLO(loIndex),
        onChanged: (selectedLO) {
          if (availableLOs.isNotEmpty && selectedLO != 'No LOs available') {
            goDetailsProvider.setPathwaySelectedLO(loIndex, selectedLO);
          }
        },
      ),
    );
  }

  Widget _buildFirstLODropdown(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: availableLOs.isEmpty ? ['No LOs available'] : availableLOs,
        value: goDetailsProvider.getPathwayFirstSelectedLO(loIndex),
        onChanged: (selectedLO) {
          if (availableLOs.isNotEmpty && selectedLO != 'No LOs available') {
            print('🔄 First LO Selection - LO: ${loIndex + 1}, Selected: $selectedLO');
            goDetailsProvider.setPathwayFirstSelectedLO(loIndex, selectedLO);
            print('✅ Saved First Selected LO for LO-${loIndex + 1}: $selectedLO');
          } else {
            print('❌ First LO NOT saved - Invalid selection: $selectedLO');
          }
        },
      ),
    );
  }

  Widget _buildSecondLODropdown(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: availableLOs.isEmpty ? ['No LOs available'] : availableLOs,
        value: goDetailsProvider.getPathwaySecondSelectedLO(loIndex),
        onChanged: (selectedLO) {
          if (availableLOs.isNotEmpty && selectedLO != 'No LOs available') {
            print('🔄 Second LO Selection - LO: ${loIndex + 1}, Selected: $selectedLO');
            goDetailsProvider.setPathwaySecondSelectedLO(loIndex, selectedLO);
            print('✅ Saved Second Selected LO for LO-${loIndex + 1}: $selectedLO');
          } else {
            print('❌ Second LO NOT saved - Invalid selection: $selectedLO');
          }
        },
      ),
    );
  }

  // Additional pathway creation helper methods
  Widget _buildRecursiveInputField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    return Container(
      height: 24,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: TextField(
        decoration: const InputDecoration(
          border: InputBorder.none,
          isDense: true,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          fillColor: Colors.transparent,
          hoverColor: Colors.transparent,
          contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 7),
          hintText: 'Input number',
          hintStyle: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
      ),
    );
  }

   /// Creates GoModel from current controller values
  GoModel _createGoModelFromControllers(GoDetailsProvider goDetailsProvider) {
    print('\n🚀 ===== CREATING GO MODEL FROM CONTROLLERS =====');
    print('📝 Solution Name: ${goDetailsProvider.solutionController.text.trim()}');
    print('📝 Description: ${goDetailsProvider.descriptionController.text.trim()}');
    print('👤 Selected Role: ${goDetailsProvider.selectedRole?.name ?? 'None'}');

    // Create GlobalObjectives with solution name and description
    final globalObjectives = GlobalObjectives(
      name: goDetailsProvider.solutionController.text.trim(),
      description: goDetailsProvider.descriptionController.text.trim(),
      naturalLanguage: goDetailsProvider.solutionController.text.trim(),
      roleType: goDetailsProvider.selectedRole?.name ?? '',
      version: "1.0",
      status: "Active",
      classification: "Process",
    );

    // Create LocalObjectivesList from LO names
    final localObjectivesList = <LocalObjectivesList>[];
    print('\n📋 Processing ${goDetailsProvider.localObjectives.length} Local Objectives:');

    for (int i = 0; i < goDetailsProvider.localObjectives.length; i++) {
      final loName = goDetailsProvider.localObjectives[i];
      if (loName.isNotEmpty) {
        print('\n🎯 LO-${i + 1}: $loName');
        print('   🔧 Pathway Creation Open: ${goDetailsProvider.isPathwayCreationOpen(i)}');

        // Create pathway data if pathway creation is open for this LO
        PathwayData? pathwayData;
        if (goDetailsProvider.isPathwayCreationOpen(i)) {
          print('   🛤️  Creating pathway data for LO-${i + 1}...');
          pathwayData = _createPathwayData(i, goDetailsProvider);
          print('   ✅ Pathway data created: ${pathwayData != null ? 'SUCCESS' : 'FAILED'}');
        } else {
          print('   ⏭️  Skipping pathway data creation (pathway creation not open)');
        }

        final roleType = goDetailsProvider.getPathwaySelectedRole(i)?.name;
        final isTerminal = goDetailsProvider.getPathwaySelectedType(i) == 'Terminal';

        print('   👤 Role Type: ${roleType ?? 'None'}');
        print('   🏁 Terminal: $isTerminal');

        localObjectivesList.add(LocalObjectivesList(
          loNumber: i + 1,
          name: loName,
          version: "1.0",
          status: "Active",
          naturalLanguage: loName,
          goId: "go_${DateTime.now().millisecondsSinceEpoch}",
          loId: "lo_${i + 1}_${DateTime.now().millisecondsSinceEpoch}",
          roleType: roleType,
          terminal: isTerminal,
          pathwayData: pathwayData,
        ));

        print('   ✅ LO-${i + 1} added to model');
      } else {
        print('🚫 LO-${i + 1}: Skipped (empty name)');
      }
    }

    // Create and return GoModel
    print('\n🎉 ===== GO MODEL CREATION COMPLETED =====');
    print('📊 Total LOs in model: ${localObjectivesList.length}');
    print('🛤️  LOs with pathway data: ${localObjectivesList.where((lo) => lo.pathwayData != null).length}');
    print('🚀 GoModel ready for validation!\n');

    return GoModel(
      globalObjectives: globalObjectives,
      localObjectivesList: localObjectivesList,
    );
  }

  /// Creates PathwayData from provider data for a specific LO
  PathwayData _createPathwayData(int loIndex, GoDetailsProvider goDetailsProvider) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);
    final selectedRole = goDetailsProvider.getPathwaySelectedRole(loIndex);

    print('     🔧 Creating PathwayData for LO-${loIndex + 1}:');
    print('     📝 Type: $selectedType');
    print('     👤 Role: ${selectedRole?.name ?? 'None'}');

    PathwayData pathwayData = PathwayData(
      selectedRole: selectedRole?.name,
      selectedType: selectedType,
    );

    switch (selectedType) {
      case 'Sequential':
        final selectedLO = goDetailsProvider.getPathwaySelectedLO(loIndex);
        print('     🔗 Sequential - Selected LO: $selectedLO');
        pathwayData.sequentialData = SequentialPathwayData(
          selectedLO: selectedLO,
        );
        break;

      case 'Alternative':
        final firstLO = goDetailsProvider.getPathwayFirstSelectedLO(loIndex);
        final firstAttr = goDetailsProvider.getPathwayFirstEntityAttribute(loIndex);
        final firstCond = goDetailsProvider.getPathwayFirstCondition(loIndex);
        final firstAttrAfter = goDetailsProvider.getPathwayFirstEntityAttributeAfterCondition(loIndex);
        final secondLO = goDetailsProvider.getPathwaySecondSelectedLO(loIndex);
        final secondAttr = goDetailsProvider.getPathwaySecondEntityAttribute(loIndex);
        final secondCond = goDetailsProvider.getPathwaySecondCondition(loIndex);
        final secondAttrAfter = goDetailsProvider.getPathwaySecondEntityAttributeAfterCondition(loIndex);

        print('     🔀 Alternative Pathway Data:');
        print('       1️⃣ First: LO=$firstLO, Attr=$firstAttr, Cond=$firstCond, AttrAfter=$firstAttrAfter');
        print('       2️⃣ Second: LO=$secondLO, Attr=$secondAttr, Cond=$secondCond, AttrAfter=$secondAttrAfter');

        pathwayData.alternativeData = AlternativePathwayData(
          firstSelectedLO: firstLO,
          firstEntityAttribute: firstAttr,
          firstCondition: firstCond,
          firstEntityAttributeAfterCondition: firstAttrAfter,
          secondSelectedLO: secondLO,
          secondEntityAttribute: secondAttr,
          secondCondition: secondCond,
          secondEntityAttributeAfterCondition: secondAttrAfter,
        );
        break;

      case 'Parallel':
        final firstLO = goDetailsProvider.getPathwayFirstSelectedLO(loIndex);
        final firstAttr = goDetailsProvider.getPathwayFirstEntityAttribute(loIndex);
        final firstCond = goDetailsProvider.getPathwayFirstCondition(loIndex);
        final firstAttrAfter = goDetailsProvider.getPathwayFirstEntityAttributeAfterCondition(loIndex);
        final secondLO = goDetailsProvider.getPathwaySecondSelectedLO(loIndex);
        final secondAttr = goDetailsProvider.getPathwaySecondEntityAttribute(loIndex);
        final secondCond = goDetailsProvider.getPathwaySecondCondition(loIndex);
        final secondAttrAfter = goDetailsProvider.getPathwaySecondEntityAttributeAfterCondition(loIndex);

        print('     ⚡ Parallel Pathway Data:');
        print('       1️⃣ First: LO=$firstLO, Attr=$firstAttr, Cond=$firstCond, AttrAfter=$firstAttrAfter');
        print('       2️⃣ Second: LO=$secondLO, Attr=$secondAttr, Cond=$secondCond, AttrAfter=$secondAttrAfter');

        pathwayData.parallelData = ParallelPathwayData(
          firstSelectedLO: firstLO,
          firstEntityAttribute: firstAttr,
          firstCondition: firstCond,
          firstEntityAttributeAfterCondition: firstAttrAfter,
          secondSelectedLO: secondLO,
          secondEntityAttribute: secondAttr,
          secondCondition: secondCond,
          secondEntityAttributeAfterCondition: secondAttrAfter,
        );
        break;

      case 'Recursive':
        print('     🔄 Recursive - Setting isRecursive: true');
        pathwayData.recursiveData = RecursivePathwayData(
          isRecursive: true,
        );
        break;

      case 'Terminal':
        print('     🏁 Terminal - Setting isTerminal: true');
        pathwayData.isTerminal = true;
        break;

      default:
        print('     ❌ Unknown pathway type: $selectedType');
    }

    print('     ✅ PathwayData creation completed for LO-${loIndex + 1}');
    return pathwayData;
  }
}
