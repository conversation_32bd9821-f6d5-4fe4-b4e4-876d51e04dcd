// To parse this JSON data, do
//
//     final chaptersResponse = chaptersResponseFromJson(jsonString);

import 'dart:convert';
import 'books_model.dart';

ChaptersResponse chaptersResponseFromJson(String str) => ChaptersResponse.fromJson(json.decode(str));

String chaptersResponseToJson(ChaptersResponse data) => json.encode(data.toJson());

class ChaptersResponse {
    List<ChapterModel>? chapters;
    BooksModel? book;
    int? totalCount;
    int? filteredCount;

    ChaptersResponse({
        this.chapters,
        this.book,
        this.totalCount,
        this.filteredCount,
    });

    ChaptersResponse copyWith({
        List<ChapterModel>? chapters,
        BooksModel? book,
        int? totalCount,
        int? filteredCount,
    }) => 
        ChaptersResponse(
            chapters: chapters ?? this.chapters,
            book: book ?? this.book,
            totalCount: totalCount ?? this.totalCount,
            filteredCount: filteredCount ?? this.filteredCount,
        );

    factory ChaptersResponse.fromJson(Map<String, dynamic> json) => ChaptersResponse(
        chapters: json["chapters"] == null ? [] : List<ChapterModel>.from(json["chapters"]!.map((x) => ChapterModel.fromJson(x))),
        book: json["book"] == null ? null : BooksModel.fromJson(json["book"]),
        totalCount: json["total_count"],
        filteredCount: json["filtered_count"],
    );

    Map<String, dynamic> toJson() => {
        "chapters": chapters == null ? [] : List<dynamic>.from(chapters!.map((x) => x.toJson())),
        "book": book?.toJson(),
        "total_count": totalCount,
        "filtered_count": filteredCount,
    };
}

class ChapterModel {
  int? id;
  String? name;
  String? description;
  String? tenantId;
  int? bookId;
  DateTime? createdAt;
  String? createdBy;
  DateTime? updatedAt;
  String? updatedBy;

  // UI state fields for dynamic loading
  bool isLoading;
  String? errorMessage;
  bool isExpanded;
  bool isSelected;
  List<dynamic> children;

  ChapterModel({
    this.id,
    this.name,
    this.description,
    this.tenantId,
    this.bookId,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.isLoading = false,
    this.errorMessage,
    this.isExpanded = false,
    this.isSelected = false,
    this.children = const [],
    // Backward compatibility parameters
    String? chapterId,
    String? chapterName,
    String? bookName,
    int? objectiveCount,
  }) {
    // Handle backward compatibility
    if (chapterId != null && id == null) {
      id = int.tryParse(chapterId);
    }
    if (chapterName != null && name == null) {
      name = chapterName;
    }
  }

  // Getters for backward compatibility
  String? get chapterId => id?.toString();
  String? get chapterName => name;
  String? get bookName => null; // Will be provided from the book object in response
  int? get objectiveCount => 0; // Default value for backward compatibility

  ChapterModel copyWith({
    int? id,
    String? name,
    String? description,
    String? tenantId,
    int? bookId,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool? isLoading,
    String? errorMessage,
    bool? isExpanded,
    bool? isSelected,
    List<dynamic>? children,
  }) =>
      ChapterModel(
        id: id ?? this.id,
        name: name ?? this.name,
        description: description ?? this.description,
        tenantId: tenantId ?? this.tenantId,
        bookId: bookId ?? this.bookId,
        createdAt: createdAt ?? this.createdAt,
        createdBy: createdBy ?? this.createdBy,
        updatedAt: updatedAt ?? this.updatedAt,
        updatedBy: updatedBy ?? this.updatedBy,
        isLoading: isLoading ?? this.isLoading,
        errorMessage: errorMessage ?? this.errorMessage,
        isExpanded: isExpanded ?? this.isExpanded,
        isSelected: isSelected ?? this.isSelected,
        children: children ?? this.children,
      );

  factory ChapterModel.fromJson(Map<String, dynamic> json) => ChapterModel(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        tenantId: json["tenant_id"],
        bookId: json["book_id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
        // UI state fields with default values (not from API)
        isLoading: false,
        errorMessage: null,
        isExpanded: false,
        isSelected: false,
        children: [],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "tenant_id": tenantId,
        "book_id": bookId,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
        // UI state fields
        "is_loading": isLoading,
        "error_message": errorMessage,
        "is_expanded": isExpanded,
        "is_selected": isSelected,
        "children": children,
      };
}
