import 'package:flutter/material.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/theme/spacing.dart'; // adjust as needed

class CustomTabHeader extends StatefulWidget {
  final Function(String)? onTabSelected;

  const CustomTabHeader({Key? key, this.onTabSelected}) : super(key: key);

  @override
  _CustomTabHeaderState createState() => _CustomTabHeaderState();
}

class _CustomTabHeaderState extends State<CustomTabHeader> {
  String _selectedTab = 'My Library';
  String? _hoveredTab;

  final List<_TabItem> _tabs = [
    _TabItem('My Library', 'assets/images/my_library/my_library_icon.svg'),
    _TabItem('Organizer', 'assets/images/my_library/my_library_organizer.svg'),
    _TabItem('Testing', 'assets/images/my_library/my_library_testing.svg'),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
     color: Color(0xffF7F9FB),
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm, vertical: AppSpacing.sm),
        
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const SizedBox(width: 100),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _tabs.map((tab) {
              final bool isSelected = tab.label == _selectedTab;
              final bool isHovered = tab.label == _hoveredTab;

              final Color bgColor = isSelected
                  ? Colors.black
                  : isHovered
                      ? Colors.white
                      : Colors.transparent;

              final Color textColor = isSelected
                  ? Colors.white
                  : isHovered
                      ? Colors.black
                      : Colors.black;

              final Color iconColor = textColor;

              final BorderSide borderSide = isHovered && !isSelected
                  ? BorderSide(color: Colors.black)
                  : BorderSide(color: Colors.transparent);

              return MouseRegion(
                onEnter: (_) => setState(() => _hoveredTab = tab.label),
                onExit: (_) => setState(() => _hoveredTab = null),
                child: GestureDetector(
                  onTap: () {
                    setState(() => _selectedTab = tab.label);
                    if (widget.onTabSelected != null) {
                      widget.onTabSelected!(tab.label);
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      color: bgColor,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.fromBorderSide(borderSide),
                    ),
                    child: Row(
                      children: [
                        CustomImage.asset(
                          tab.svgAssetPath,
                          width: 12,
                          height: 12,
                          fit: BoxFit.contain,
                          color: iconColor,
                        ).toWidget(),
                        const SizedBox(width: 6),
                        Text(
                          tab.label,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'TiemposText',
                            color: textColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(width: 100),
        ],
      ),
    );
  }
}

class _TabItem {
  final String label;
  final String svgAssetPath;

  _TabItem(this.label, this.svgAssetPath);
}
