import 'package:flutter/foundation.dart';
import '../screens/web/static_flow/my_library_service.dart';
import '../screens/web/static_flow/get_all_my_library_model.dart';
import '../utils/logger.dart';

class MyLibraryProvider extends ChangeNotifier {
  final MyLibraryService _libraryService = MyLibraryService();

  // State variables
  GetAllMylibrarylist? _libraryData;
  List<RolesPostgre> _roles = [];
  bool _isLoading = false;
  String? _error;
  RolesPostgre? _selectedRole;

  // Getters
  GetAllMylibrarylist? get libraryData => _libraryData;
  List<RolesPostgre> get roles => _roles;
  bool get isLoading => _isLoading;
  String? get error => _error;
  RolesPostgre? get selectedRole => _selectedRole;

  /// Fetches library data from the API
  Future<void> fetchLibraryData({String tenantId = 'T1008'}) async {
    if (_isLoading) return; // Prevent multiple simultaneous requests

    _setLoading(true);
    _setError(null);

    try {
      Logger.info('MyLibraryProvider: Starting to fetch library data...');

      final libraryResponse = await MyLibraryService.getAllLibraryData(
        tenantId: tenantId,
      );

      if (libraryResponse != null) {
        _libraryData = libraryResponse;
        
        // Extract roles from the nested structure: data.library.roles.postgres
        _roles = libraryResponse.data?.dataLibrary?.roles?.postgres ?? [];
        
        Logger.info('MyLibraryProvider: Successfully loaded ${_roles.length} roles');

        // Debug: Print role names
        for (var role in _roles) {
          Logger.info('MyLibraryProvider: Role - ID: ${role.roleId}, Name: ${role.name}');
        }
      } else {
        _setError('Failed to fetch library data');
        Logger.error('MyLibraryProvider: Library response was null');
      }

    } catch (e) {
      _setError('An unexpected error occurred: $e');
      Logger.error('MyLibraryProvider: Exception while fetching library data - $e');

    } finally {
      _setLoading(false);
    }
  }

  /// Gets role names for dropdown display
  List<String> getRoleNames() {
    return _roles
        .where((role) => role.name != null && role.name!.isNotEmpty)
        .map((role) => role.name!)
        .toList();
  }

  /// Sets the selected role
  void setSelectedRole(RolesPostgre? role) {
    if (_selectedRole != role) {
      _selectedRole = role;
      Logger.info('MyLibraryProvider: Selected role changed to: ${role?.name ?? 'None'}');
      notifyListeners();
    }
  }

  /// Sets the selected role by name
  void setSelectedRoleByName(String? roleName) {
    if (roleName == null || roleName.isEmpty) {
      setSelectedRole(null);
      return;
    }

    try {
      final role = _roles.firstWhere((role) => role.name == roleName);
      setSelectedRole(role);
    } catch (e) {
      Logger.warning('MyLibraryProvider: Role with name $roleName not found');
      setSelectedRole(null);
    }
  }

  /// Clears the selected role
  void clearSelectedRole() {
    setSelectedRole(null);
  }

  /// Refreshes the library data
  Future<void> refreshLibraryData({String tenantId = 'T1008'}) async {
    Logger.info('MyLibraryProvider: Refreshing library data...');
    _libraryData = null;
    _roles.clear();
    await fetchLibraryData(tenantId: tenantId);
  }

  /// Gets a role by ID
  RolesPostgre? getRoleById(String id) {
    try {
      return _roles.firstWhere((role) => role.roleId == id);
    } catch (e) {
      Logger.warning('MyLibraryProvider: Role with ID $id not found');
      return null;
    }
  }

  /// Gets a role by name
  RolesPostgre? getRoleByName(String name) {
    try {
      return _roles.firstWhere((role) => role.name == name);
    } catch (e) {
      Logger.warning('MyLibraryProvider: Role with name $name not found');
      return null;
    }
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// Clears all data
  void clear() {
    _libraryData = null;
    _roles.clear();
    _selectedRole = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
