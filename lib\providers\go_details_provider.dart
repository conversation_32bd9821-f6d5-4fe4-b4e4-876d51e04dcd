import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/models/solution/go_model.dart';
import 'package:nsl/utils/logger.dart';

enum GoDetailsStep {
  initial,
  afterValidation,
  afterLocalObjectives,
}

class GoDetailsProvider extends ChangeNotifier {
  // Controllers
  final TextEditingController solutionController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController localObjectiveController =
      TextEditingController();

  // State variables
  GoDetailsStep _currentStep = GoDetailsStep.initial;
  PostgresRole? _selectedRole;
  bool _isValidating = false;
  String? _validationError;

  // Mock data for demonstration - in real app this would come from API
  String? _generatedDescription;
  List<String> _localObjectives = [];
  // Local objective details state
  bool _showLocalObjectiveDetails = false;
  int? _selectedLocalObjectiveIndex;

  // Local objective details dropdown selections
  Map<int, String?> _loFunctionTypes = {}; // Track function type for each LO
  Map<int, PostgresRole?> _loSelectedRoles =
      {}; // Track selected roles for each LO
  Map<int, String?> _loExecutionRights =
      {}; // Track execution rights for each LO

  // Multiple role rows for each LO
  Map<int, List<PostgresRole?>> _loMultipleRoles =
      {}; // Track multiple roles for each LO
  Map<int, List<String?>> _loMultipleExecutionRights =
      {}; // Track multiple execution rights for each LO

  // Pathway creation state

  Map<int, bool> _pathwayCreationStates =
      {}; // Track which LOs have pathway creation open
  Map<int, PostgresRole?> _pathwaySelectedRoles =
      {}; // Track selected roles for each LO
  Map<int, String?> _pathwaySelectedTypes =
      {}; // Track selected types for each LO
  Map<int, String?> _pathwaySelectedLOs =
      {}; // Track selected LOs for sequential type

  // Alternative/Parallel pathway data storage - Dynamic lists
  Map<int, List<PathwayEntry>> _pathwayEntries = {}; // Dynamic pathway entries for each LO

  // LO insertion state
  final Map<int, bool> _loInsertionStates =
      {}; // Track which LOs have insertion text field open
  final Map<int, TextEditingController> _loInsertionControllers =
      {}; // Controllers for insertion text fields

  // Getters
  GoDetailsStep get currentStep => _currentStep;
  PostgresRole? get selectedRole => _selectedRole;
  bool get isValidating => _isValidating;
  String? get validationError => _validationError;
  String? get generatedDescription => _generatedDescription;
  List<String> get localObjectives => _localObjectives;

  // Pathway creation getters
  bool isPathwayCreationOpen(int loIndex) =>
      _pathwayCreationStates[loIndex] ?? false;
  PostgresRole? getPathwaySelectedRole(int loIndex) =>
      _pathwaySelectedRoles[loIndex];
  String? getPathwaySelectedType(int loIndex) => _pathwaySelectedTypes[loIndex];
  String? getPathwaySelectedLO(int loIndex) => _pathwaySelectedLOs[loIndex];

  // Alternative/Parallel pathway getters
  List<PathwayEntry> getPathwayEntries(int loIndex) => _pathwayEntries[loIndex] ?? [];

  // Get specific entry by index
  PathwayEntry? getPathwayEntry(int loIndex, int entryIndex) {
    final entries = _pathwayEntries[loIndex];
    if (entries != null && entryIndex < entries.length) {
      return entries[entryIndex];
    }
    return null;
  }

  // Get count of pathway entries for a specific LO
  int getPathwayEntryCount(int loIndex) => _pathwayEntries[loIndex]?.length ?? 0;

  // Backward compatibility getters (for existing UI)
  String? getPathwayFirstSelectedLO(int loIndex) => getPathwayEntry(loIndex, 0)?.selectedLO;
  String? getPathwayFirstEntityAttribute(int loIndex) => getPathwayEntry(loIndex, 0)?.entityAttribute;
  String? getPathwayFirstCondition(int loIndex) => getPathwayEntry(loIndex, 0)?.condition;
  String? getPathwayFirstEntityAttributeAfterCondition(int loIndex) => getPathwayEntry(loIndex, 0)?.entityAttributeAfterCondition;

  String? getPathwaySecondSelectedLO(int loIndex) => getPathwayEntry(loIndex, 1)?.selectedLO;
  String? getPathwaySecondEntityAttribute(int loIndex) => getPathwayEntry(loIndex, 1)?.entityAttribute;
  String? getPathwaySecondCondition(int loIndex) => getPathwayEntry(loIndex, 1)?.condition;
  String? getPathwaySecondEntityAttributeAfterCondition(int loIndex) => getPathwayEntry(loIndex, 1)?.entityAttributeAfterCondition;

  // LO insertion getters
  bool isLoInsertionOpen(int loIndex) => _loInsertionStates[loIndex] ?? false;
  TextEditingController? getLoInsertionController(int loIndex) =>
      _loInsertionControllers[loIndex];

  // Local objective details getters
  bool get showLocalObjectiveDetails => _showLocalObjectiveDetails;
  int? get selectedLocalObjectiveIndex => _selectedLocalObjectiveIndex;

  // Local objective details dropdown getters
  String? getLoFunctionType(int loIndex) => _loFunctionTypes[loIndex];
  PostgresRole? getLoSelectedRole(int loIndex) => _loSelectedRoles[loIndex];
  String? getLoExecutionRights(int loIndex) => _loExecutionRights[loIndex];

  // Multiple roles getters
  List<PostgresRole?> getLoMultipleRoles(int loIndex) =>
      _loMultipleRoles[loIndex] ?? [];
  List<String?> getLoMultipleExecutionRights(int loIndex) =>
      _loMultipleExecutionRights[loIndex] ?? [];
  int getLoRoleRowsCount(int loIndex) => _loMultipleRoles[loIndex]?.length ?? 0;
  // Get available LOs for sequential selection (LOs after the current one)
  List<String> getAvailableLOsForSequential(int currentLoIndex) {
    if (currentLoIndex >= _localObjectives.length - 1) return [];
    return _localObjectives
        .asMap()
        .entries
        .where((entry) => entry.key > currentLoIndex)
        .map((entry) => entry.value) // Return only the LO name without prefix
        .toList();
  }

  /// Sets the selected role
  void setSelectedRole(PostgresRole? role) {
    if (_selectedRole != role) {
      _selectedRole = role;
      Logger.info(
          'GoDetailsProvider: Selected role changed to: ${role?.name ?? 'None'}');
      notifyListeners();
    }
  }

  /// Validates the solution and moves to next step
  Future<void> validateSolution() async {
    if (solutionController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info(
          'GoDetailsProvider: Validating solution: ${solutionController.text}');

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock response - in real app this would come from API
      _generateMockResponse();

      _currentStep = GoDetailsStep.afterValidation;
      Logger.info('GoDetailsProvider: Solution validated successfully');
    } catch (e) {
      Logger.error('GoDetailsProvider: Validation error - $e');
    } finally {
      _setValidating(false);
    }
  }

  /// Validates the solution with GoModel data and moves to next step
  Future<void> validateSolutionWithGoModel(GoModel goModel) async {
    if (solutionController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info(
          'GoDetailsProvider: Validating solution with GoModel: ${goModel.globalObjectives?.name}');

      // Log the GoModel data being saved
      Logger.info('GoDetailsProvider: Solution Name: ${goModel.globalObjectives?.name}');
      Logger.info('GoDetailsProvider: Solution Description: ${goModel.globalObjectives?.description}');
      Logger.info('GoDetailsProvider: Solution Agent Type: ${goModel.globalObjectives?.roleType}');
      // Logger.info('GoDetailsProvider: Solution Description: ${goModel.globalObjectives?.description}');
      // Logger.info('GoDetailsProvider: Local Objectives Count: ${goModel.localObjectivesList?.length ?? 0}');

      // Log each LO name
      if (goModel.localObjectivesList != null) {
        for (int i = 0; i < goModel.localObjectivesList!.length; i++) {
          final lo = goModel.localObjectivesList![i];
          Logger.info('GoDetailsProvider: LO ${i + 1}: ${lo.name}');
        }
      }

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock response - in real app this would come from API
      _generateMockResponse();

      _currentStep = GoDetailsStep.afterValidation;
      Logger.info('GoDetailsProvider: Solution with GoModel validated successfully');
    } catch (e) {
      Logger.error('GoDetailsProvider: Validation with GoModel error - $e');
    } finally {
      _setValidating(false);
    }
  }

  /// Generates mock response for demonstration
  void _generateMockResponse() {
    final solutionText = solutionController.text.trim();

    // Generate description based on solution
    _generatedDescription =
        'Comprehensive ${solutionText.toLowerCase()} process from registration to welcome completion';
    descriptionController.text = _generatedDescription ?? '';

    // Generate mock local objectives
    _localObjectives = [
      'Type LO name with full stop (.)',
    ];

    notifyListeners();
  }

  /// Processes local objectives from text input
  void processLocalObjectives() {
    final inputText = localObjectiveController.text.trim();

    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No local objectives text provided');
      return;
    }

    // Split by full stops and clean up
    final objectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (objectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid local objectives found');
      return;
    }

    // Capitalize first letter of each objective
    _localObjectives = objectives
        .map((obj) => obj.isEmpty
            ? obj
            : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
        .toList();

    _currentStep = GoDetailsStep.afterLocalObjectives;

    Logger.info(
        'GoDetailsProvider: Processed ${_localObjectives.length} local objectives: $_localObjectives');
    notifyListeners();
  }

  /// Toggles pathway creation for a specific LO
  void togglePathwayCreation(int loIndex) {
    _pathwayCreationStates[loIndex] =
        !(_pathwayCreationStates[loIndex] ?? false);

    // Clear selections if closing
    if (!_pathwayCreationStates[loIndex]!) {
      _pathwaySelectedRoles.remove(loIndex);
      _pathwaySelectedTypes.remove(loIndex);
      _pathwaySelectedLOs.remove(loIndex);

      // Clear alternative/parallel pathway data
      _pathwayEntries.remove(loIndex);
    }

    Logger.info(
        'GoDetailsProvider: Toggled pathway creation for LO-${loIndex + 1}: ${_pathwayCreationStates[loIndex]}');
    notifyListeners();
  }

  /// Sets the selected role for pathway creation
  void setPathwaySelectedRole(int loIndex, PostgresRole? role) {
    _pathwaySelectedRoles[loIndex] = role;
    Logger.info(
        'GoDetailsProvider: Set pathway role for LO-${loIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets the selected type for pathway creation
  void setPathwaySelectedType(int loIndex, String? type) {
    _pathwaySelectedTypes[loIndex] = type;

    // Clear LO selection if type changes
    if (type != 'Sequential') {
      _pathwaySelectedLOs.remove(loIndex);
    }

    Logger.info(
        'GoDetailsProvider: Set pathway type for LO-${loIndex + 1}: $type');
    notifyListeners();
  }

  /// Sets the selected LO for sequential pathway
  void setPathwaySelectedLO(int loIndex, String? selectedLO) {
    _pathwaySelectedLOs[loIndex] = selectedLO;
    Logger.info(
        'GoDetailsProvider: Set pathway LO for LO-${loIndex + 1}: $selectedLO');
    notifyListeners();
  }

  // Alternative/Parallel pathway setters - Dynamic approach
  void _ensurePathwayEntryExists(int loIndex, int entryIndex) {
    if (_pathwayEntries[loIndex] == null) {
      _pathwayEntries[loIndex] = [];
    }

    // Ensure we have enough entries
    while (_pathwayEntries[loIndex]!.length <= entryIndex) {
      _pathwayEntries[loIndex]!.add(PathwayEntry());
    }
  }

  void setPathwayEntrySelectedLO(int loIndex, int entryIndex, String? selectedLO) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].selectedLO = selectedLO;
    Logger.info('GoDetailsProvider: Set LO for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $selectedLO');
    notifyListeners();
  }

  void setPathwayEntryEntityAttribute(int loIndex, int entryIndex, String? attribute) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].entityAttribute = attribute;
    Logger.info('GoDetailsProvider: Set entity attribute for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $attribute');
    notifyListeners();
  }

  void setPathwayEntryCondition(int loIndex, int entryIndex, String? condition) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].condition = condition;
    Logger.info('GoDetailsProvider: Set condition for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $condition');
    notifyListeners();
  }

  void setPathwayEntryEntityAttributeAfterCondition(int loIndex, int entryIndex, String? attribute) {
    _ensurePathwayEntryExists(loIndex, entryIndex);
    _pathwayEntries[loIndex]![entryIndex].entityAttributeAfterCondition = attribute;
    Logger.info('GoDetailsProvider: Set entity attribute after condition for LO-${loIndex + 1}, Entry-${entryIndex + 1}: $attribute');
    notifyListeners();
  }

  // Add new pathway entry
  void addPathwayEntry(int loIndex) {
    if (_pathwayEntries[loIndex] == null) {
      _pathwayEntries[loIndex] = [];
    }
    _pathwayEntries[loIndex]!.add(PathwayEntry());
    Logger.info('GoDetailsProvider: Added new pathway entry for LO-${loIndex + 1}. Total entries: ${_pathwayEntries[loIndex]!.length}');
    notifyListeners();
  }

  // Remove pathway entry
  void removePathwayEntry(int loIndex, int entryIndex) {
    if (_pathwayEntries[loIndex] != null && entryIndex < _pathwayEntries[loIndex]!.length) {
      _pathwayEntries[loIndex]!.removeAt(entryIndex);
      Logger.info('GoDetailsProvider: Removed pathway entry ${entryIndex + 1} for LO-${loIndex + 1}. Remaining entries: ${_pathwayEntries[loIndex]!.length}');
      notifyListeners();
    }
  }

  // Backward compatibility setters (for existing UI)
  void setPathwayFirstSelectedLO(int loIndex, String? selectedLO) {
    setPathwayEntrySelectedLO(loIndex, 0, selectedLO);
  }

  void setPathwayFirstEntityAttribute(int loIndex, String? attribute) {
    setPathwayEntryEntityAttribute(loIndex, 0, attribute);
  }

  void setPathwayFirstCondition(int loIndex, String? condition) {
    setPathwayEntryCondition(loIndex, 0, condition);
  }

  void setPathwayFirstEntityAttributeAfterCondition(int loIndex, String? attribute) {
    setPathwayEntryEntityAttributeAfterCondition(loIndex, 0, attribute);
  }

  void setPathwaySecondSelectedLO(int loIndex, String? selectedLO) {
    setPathwayEntrySelectedLO(loIndex, 1, selectedLO);
  }

  void setPathwaySecondEntityAttribute(int loIndex, String? attribute) {
    setPathwayEntryEntityAttribute(loIndex, 1, attribute);
  }

  void setPathwaySecondCondition(int loIndex, String? condition) {
    setPathwayEntryCondition(loIndex, 1, condition);
  }

  void setPathwaySecondEntityAttributeAfterCondition(int loIndex, String? attribute) {
    setPathwayEntryEntityAttributeAfterCondition(loIndex, 1, attribute);
  }

  /// Shows local objective details for a specific LO
  void setShowLocalObjectiveDetails(int loIndex) {
    _showLocalObjectiveDetails = true;
    _selectedLocalObjectiveIndex = loIndex;
    Logger.info(
        'GoDetailsProvider: Showing local objective details for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Hides local objective details
  void hideLocalObjectiveDetails() {
    _showLocalObjectiveDetails = false;
    _selectedLocalObjectiveIndex = null;
    Logger.info('GoDetailsProvider: Hiding local objective details');
    notifyListeners();
  }

  /// Sets the function type for a specific LO
  void setLoFunctionType(int loIndex, String? functionType) {
    _loFunctionTypes[loIndex] = functionType;
    Logger.info(
        'GoDetailsProvider: Set function type for LO-${loIndex + 1}: $functionType');
    notifyListeners();
  }

  /// Sets the selected role for a specific LO
  void setLoSelectedRole(int loIndex, PostgresRole? role) {
    _loSelectedRoles[loIndex] = role;
    Logger.info(
        'GoDetailsProvider: Set role for LO-${loIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets the execution rights for a specific LO
  void setLoExecutionRights(int loIndex, String? executionRights) {
    _loExecutionRights[loIndex] = executionRights;
    Logger.info(
        'GoDetailsProvider: Set execution rights for LO-${loIndex + 1}: $executionRights');
    notifyListeners();
  }

  /// Adds a new role row for a specific LO
  void addLoRoleRow(int loIndex) {
    if (_loMultipleRoles[loIndex] == null) {
      _loMultipleRoles[loIndex] = [];
      _loMultipleExecutionRights[loIndex] = [];
    }
    _loMultipleRoles[loIndex]!.add(null);
    _loMultipleExecutionRights[loIndex]!.add(null);
    Logger.info(
        'GoDetailsProvider: Added role row for LO-${loIndex + 1}. Total rows: ${_loMultipleRoles[loIndex]!.length}');
    notifyListeners();
  }

  /// Sets a role for a specific row in a specific LO
  void setLoMultipleRole(int loIndex, int rowIndex, PostgresRole? role) {
    if (_loMultipleRoles[loIndex] == null) {
      _loMultipleRoles[loIndex] = [];
    }
    // Ensure the list is large enough
    while (_loMultipleRoles[loIndex]!.length <= rowIndex) {
      _loMultipleRoles[loIndex]!.add(null);
    }
    _loMultipleRoles[loIndex]![rowIndex] = role;
    Logger.info(
        'GoDetailsProvider: Set role for LO-${loIndex + 1}, row ${rowIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets execution rights for a specific row in a specific LO
  void setLoMultipleExecutionRights(
      int loIndex, int rowIndex, String? executionRights) {
    if (_loMultipleExecutionRights[loIndex] == null) {
      _loMultipleExecutionRights[loIndex] = [];
    }
    // Ensure the list is large enough
    while (_loMultipleExecutionRights[loIndex]!.length <= rowIndex) {
      _loMultipleExecutionRights[loIndex]!.add(null);
    }
    _loMultipleExecutionRights[loIndex]![rowIndex] = executionRights;
    Logger.info(
        'GoDetailsProvider: Set execution rights for LO-${loIndex + 1}, row ${rowIndex + 1}: $executionRights');
    notifyListeners();
  }

  /// Removes a role row for a specific LO
  void removeLoRoleRow(int loIndex, int rowIndex) {
    if (_loMultipleRoles[loIndex] != null &&
        rowIndex < _loMultipleRoles[loIndex]!.length) {
      _loMultipleRoles[loIndex]!.removeAt(rowIndex);
    }
    if (_loMultipleExecutionRights[loIndex] != null &&
        rowIndex < _loMultipleExecutionRights[loIndex]!.length) {
      _loMultipleExecutionRights[loIndex]!.removeAt(rowIndex);
    }
    Logger.info(
        'GoDetailsProvider: Removed role row ${rowIndex + 1} for LO-${loIndex + 1}');
    notifyListeners();
  }

  /// Toggles LO insertion text field for a specific LO
  void toggleLoInsertion(int loIndex) {
    final isCurrentlyOpen = _loInsertionStates[loIndex] ?? false;

    // Close all other insertion fields first
    _closeAllLoInsertions();

    if (!isCurrentlyOpen) {
      // Open insertion for this LO
      _loInsertionStates[loIndex] = true;
      _loInsertionControllers[loIndex] = TextEditingController();
    }

    Logger.info(
        'GoDetailsProvider: Toggled LO insertion for LO-${loIndex + 1}: ${_loInsertionStates[loIndex]}');
    notifyListeners();
  }

  /// Closes all LO insertion text fields
  void _closeAllLoInsertions() {
    // Dispose controllers and clear states
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();
    _loInsertionStates.clear();
  }

  /// Processes and inserts new LOs after the specified index
  void processLoInsertion(int afterIndex) {
    final controller = _loInsertionControllers[afterIndex];
    if (controller == null) {
      Logger.warning(
          'GoDetailsProvider: No controller found for LO insertion at index $afterIndex');
      return;
    }

    final inputText = controller.text.trim();
    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No text provided for LO insertion');
      return;
    }

    // Split by dots and clean up
    final newObjectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (newObjectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid LOs found for insertion');
      return;
    }

    // Capitalize first letter of each objective
    final formattedObjectives = newObjectives
        .map((obj) => obj.isEmpty
            ? obj
            : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
        .toList();

    // Insert new LOs after the specified index
    _insertLocalObjectives(afterIndex, formattedObjectives);

    // Close the insertion field
    _loInsertionStates[afterIndex] = false;
    controller.dispose();
    _loInsertionControllers.remove(afterIndex);

    Logger.info(
        'GoDetailsProvider: Inserted ${formattedObjectives.length} LOs after index $afterIndex: $formattedObjectives');
    notifyListeners();
  }

  /// Inserts new local objectives at the specified position
  void _insertLocalObjectives(int afterIndex, List<String> newObjectives) {
    // Insert new objectives after the specified index
    for (int i = 0; i < newObjectives.length; i++) {
      _localObjectives.insert(afterIndex + 1 + i, newObjectives[i]);
    }

    // Update pathway creation states - shift indices for items after insertion point
    final updatedPathwayStates = <int, bool>{};
    final updatedPathwayRoles = <int, PostgresRole?>{};
    final updatedPathwayTypes = <int, String?>{};
    final updatedPathwayLOs = <int, String?>{};

    for (final entry in _pathwayCreationStates.entries) {
      final oldIndex = entry.key;
      final newIndex =
          oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayStates[newIndex] = entry.value;
    }

    for (final entry in _pathwaySelectedRoles.entries) {
      final oldIndex = entry.key;
      final newIndex =
          oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayRoles[newIndex] = entry.value;
    }

    for (final entry in _pathwaySelectedTypes.entries) {
      final oldIndex = entry.key;
      final newIndex =
          oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayTypes[newIndex] = entry.value;
    }

    for (final entry in _pathwaySelectedLOs.entries) {
      final oldIndex = entry.key;
      final newIndex =
          oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayLOs[newIndex] = entry.value;
    }

    // Replace the maps with updated indices
    _pathwayCreationStates.clear();
    _pathwayCreationStates.addAll(updatedPathwayStates);

    _pathwaySelectedRoles.clear();
    _pathwaySelectedRoles.addAll(updatedPathwayRoles);

    _pathwaySelectedTypes.clear();
    _pathwaySelectedTypes.addAll(updatedPathwayTypes);

    _pathwaySelectedLOs.clear();
    _pathwaySelectedLOs.addAll(updatedPathwayLOs);
  }

  /// Resets to initial step
  void resetToInitial() {
    _currentStep = GoDetailsStep.initial;
    _generatedDescription = null;
    _localObjectives.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _setValidationError(null);
    hideLocalObjectiveDetails();
    Logger.info('GoDetailsProvider: Reset to initial step');
    notifyListeners();
  }

  /// Clears all form data
  void clearForm() {
    solutionController.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _selectedRole = null;
    hideLocalObjectiveDetails();
    resetToInitial();
    Logger.info('GoDetailsProvider: Form cleared');
  }

  /// Private helper methods
  void _setValidating(bool validating) {
    if (_isValidating != validating) {
      _isValidating = validating;
      notifyListeners();
    }
  }

  void _setValidationError(String? error) {
    if (_validationError != error) {
      _validationError = error;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    solutionController.dispose();
    descriptionController.dispose();
    localObjectiveController.dispose();

    // Dispose LO insertion controllers
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();

    super.dispose();
  }
}
