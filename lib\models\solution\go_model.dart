import '../object_creation_model.dart';

class GoModel {
  GlobalObjectives? globalObjectives;
  ProcessOwnership? processOwnership;
  TriggerDefinition? triggerDefinition;
  List<LocalObjectivesList>? localObjectivesList;
  List<PathwayDefinition>? pathwayDefinitions;
  PerformanceMetadataClass? performanceMetadata;
  ProcessMiningSchema? processMiningSchema;
  PerformanceMetadataClass? performanceDiscoveryMetrics;
  GoModelConformanceAnalytics? conformanceAnalytics;
  GoModelAdvancedProcessIntelligence? advancedProcessIntelligence;
  List<RollbackPathway>? rollbackPathways;
  PerformanceMetadataClass? validationRules;
  List<DataConstraint>? dataConstraints;
  List<ProcessFlow>? processFlow;

  GoModel({
    this.globalObjectives,
    this.processOwnership,
    this.triggerDefinition,
    this.localObjectivesList,
    this.pathwayDefinitions,
    this.performanceMetadata,
    this.processMiningSchema,
    this.performanceDiscoveryMetrics,
    this.conformanceAnalytics,
    this.advancedProcessIntelligence,
    this.rollbackPathways,
    this.validationRules,
    this.dataConstraints,
    this.processFlow,
  });

}

class GoModelAdvancedProcessIntelligence {
  String? id;
  String? goId;
  String? naturalLanguage;
  ProcessHealthScore? processHealthScore;
  PredictionModels? predictionModels;
  OptimizationInsights? optimizationInsights;

  GoModelAdvancedProcessIntelligence({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.processHealthScore,
    this.predictionModels,
    this.optimizationInsights,
  });

}

class OptimizationInsights {
  List<String>? bottleneckElimination;
  List<String>? resourceReallocation;
  List<String>? pathwayOptimization;

  OptimizationInsights({
    this.bottleneckElimination,
    this.resourceReallocation,
    this.pathwayOptimization,
  });

}

class PredictionModels {
  CompletionTimeForecast? completionTimeForecast;

  PredictionModels({
    this.completionTimeForecast,
  });

}

class CompletionTimeForecast {
  String? algorithm;
  int? accuracy;
  String? confidenceInterval;

  CompletionTimeForecast({
    this.algorithm,
    this.accuracy,
    this.confidenceInterval,
  });

}

class ProcessHealthScore {
  int? performanceScore;
  int? complianceScore;
  int? efficiencyScore;
  int? overallHealth;

  ProcessHealthScore({
    this.performanceScore,
    this.complianceScore,
    this.efficiencyScore,
    this.overallHealth,
  });

}

class GoModelConformanceAnalytics {
  String? id;
  String? goId;
  String? naturalLanguage;
  int? complianceRate;
  PurpleExceptionPatterns? exceptionPatterns;

  GoModelConformanceAnalytics({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.complianceRate,
    this.exceptionPatterns,
  });

}

class PurpleExceptionPatterns {
  PurpleValidationFailure? validationFailure;
  SystemError? systemError;

  PurpleExceptionPatterns({
    this.validationFailure,
    this.systemError,
  });

}

class SystemError {
  int? frequency;
  List<String>? errorCategories;
  int? automaticRecoveryRate;

  SystemError({
    this.frequency,
    this.errorCategories,
    this.automaticRecoveryRate,
  });

}

class PurpleValidationFailure {
  int? frequency;
  List<String>? mostCommonFailures;
  String? resolutionTime;

  PurpleValidationFailure({
    this.frequency,
    this.mostCommonFailures,
    this.resolutionTime,
  });

}

class DataConstraint {
  String? id;
  String? goId;
  String? entity;
  String? attribute;
  String? dataType;
  String? constraintType;
  String? constraintText;
  String? errorMessage;
  String? naturalLanguage;

  DataConstraint({
    this.id,
    this.goId,
    this.entity,
    this.attribute,
    this.dataType,
    this.constraintType,
    this.constraintText,
    this.errorMessage,
    this.naturalLanguage,
  });

}

class GlobalObjectives {
  String? naturalLanguage;
  String? name;
  String? version;
  String? status;
  String? description;
  String? primaryEntity;
  String? classification;
  String? agentType;
  String? bookName;
  String? chapterName;
  String? tenantName;
  String? goId;
  String? tenantId;
  String? bookId;
  String? chapterId;
  String? versionType;
  String? roleType;

  GlobalObjectives({
    this.naturalLanguage,
    this.name,
    this.version,
    this.status,
    this.description,
    this.primaryEntity,
    this.classification,
    this.agentType,
    this.bookName,
    this.chapterName,
    this.tenantName,
    this.goId,
    this.tenantId,
    this.bookId,
    this.chapterId,
    this.versionType,
    this.roleType,
  });

}

class LocalObjectivesList {
  int? loNumber;
  String? workSource;
  bool? terminal;
  String? name;
  String? version;
  String? status;
  String? workflowSource;
  String? functionType;
  String? agentType;
  String? executionRights;
  String? tenantName;
  String? tenantId;
  String? uiType;
  String? naturalLanguage;
  String? goId;
  String? loId;
  String? roleType;

  // Pathway-related fields
  PathwayData? pathwayData;

  List<ObjectCreationModel>? entitiesList;

  LocalObjectivesList({
    this.loNumber,
    this.workSource,
    this.terminal,
    this.name,
    this.version,
    this.status,
    this.workflowSource,
    this.functionType,
    this.agentType,
    this.executionRights,
    this.tenantName,
    this.tenantId,
    this.uiType,
    this.naturalLanguage,
    this.goId,
    this.loId,
    this.entitiesList,
    this.roleType,
    this.pathwayData,
  });

}

class PathwayData {
  String? selectedRole;
  String? selectedType;

  // Sequential pathway data
  SequentialPathwayData? sequentialData;

  // Alternative pathway data
  AlternativePathwayData? alternativeData;

  // Parallel pathway data
  ParallelPathwayData? parallelData;

  // Recursive pathway data
  RecursivePathwayData? recursiveData;

  // Terminal pathway data
  bool? isTerminal;

  PathwayData({
    this.selectedRole,
    this.selectedType,
    this.sequentialData,
    this.alternativeData,
    this.parallelData,
    this.recursiveData,
    this.isTerminal,
  });
}

class SequentialPathwayData {
  String? selectedLO;

  SequentialPathwayData({
    this.selectedLO,
  });
}

class PathwayEntry {
  String? selectedLO;
  String? entityAttribute;
  String? condition;
  String? entityAttributeAfterCondition;

  PathwayEntry({
    this.selectedLO,
    this.entityAttribute,
    this.condition,
    this.entityAttributeAfterCondition,
  });

  Map<String, dynamic> toJson() {
    return {
      'selectedLO': selectedLO,
      'entityAttribute': entityAttribute,
      'condition': condition,
      'entityAttributeAfterCondition': entityAttributeAfterCondition,
    };
  }

  factory PathwayEntry.fromJson(Map<String, dynamic> json) {
    return PathwayEntry(
      selectedLO: json['selectedLO'],
      entityAttribute: json['entityAttribute'],
      condition: json['condition'],
      entityAttributeAfterCondition: json['entityAttributeAfterCondition'],
    );
  }
}

class AlternativePathwayData {
  List<PathwayEntry> pathwayEntries;

  AlternativePathwayData({
    List<PathwayEntry>? pathwayEntries,
  }) : pathwayEntries = pathwayEntries ?? [];

  Map<String, dynamic> toJson() {
    return {
      'pathwayEntries': pathwayEntries.map((entry) => entry.toJson()).toList(),
    };
  }

  factory AlternativePathwayData.fromJson(Map<String, dynamic> json) {
    return AlternativePathwayData(
      pathwayEntries: (json['pathwayEntries'] as List<dynamic>?)
          ?.map((entry) => PathwayEntry.fromJson(entry))
          .toList() ?? [],
    );
  }
}

class ParallelPathwayData {
  List<PathwayEntry> pathwayEntries;

  ParallelPathwayData({
    List<PathwayEntry>? pathwayEntries,
  }) : pathwayEntries = pathwayEntries ?? [];

  Map<String, dynamic> toJson() {
    return {
      'pathwayEntries': pathwayEntries.map((entry) => entry.toJson()).toList(),
    };
  }

  factory ParallelPathwayData.fromJson(Map<String, dynamic> json) {
    return ParallelPathwayData(
      pathwayEntries: (json['pathwayEntries'] as List<dynamic>?)
          ?.map((entry) => PathwayEntry.fromJson(entry))
          .toList() ?? [],
    );
  }
}

class RecursivePathwayData {
  bool? isRecursive;

  RecursivePathwayData({
    this.isRecursive,
  });
}

class PathwayDefinition {
  String? id;
  int? pathwayNumber;
  String? pathwayName;
  List<String>? steps;
  String? naturalLanguage;

  PathwayDefinition({
    this.id,
    this.pathwayNumber,
    this.pathwayName,
    this.steps,
    this.naturalLanguage,
  });

}

class PerformanceMetadataClass {
  String? id;
  String? goId;
  String? naturalLanguage;
  PerformanceMetadataResourcePatterns? resourcePatterns;
  MetadataData? metadataData;
  List<Rule>? rules;

  PerformanceMetadataClass({
    this.id,
    this.goId,
    this.naturalLanguage,
    this.resourcePatterns,
    this.metadataData,
    this.rules,
  });

}

class MetadataData {
  String? cycleTime;
  int? numberOfPathways;
  VolumeMetrics? volumeMetrics;
  SlaThresholds? slaThresholds;
  CriticalLoPerformance? criticalLoPerformance;

  MetadataData({
    this.cycleTime,
    this.numberOfPathways,
    this.volumeMetrics,
    this.slaThresholds,
    this.criticalLoPerformance,
  });

}

class CriticalLoPerformance {
  String? createEmployeeRecord;
  String? updateEmployeeRecord;
  String? viewEmployeeDatabase;
  String? deleteEmployeeRecord;

  CriticalLoPerformance({
    this.createEmployeeRecord,
    this.updateEmployeeRecord,
    this.viewEmployeeDatabase,
    this.deleteEmployeeRecord,
  });

}

class SlaThresholds {
  String? recordCreation;
  String? recordUpdate;
  String? recordSearch;
  String? recordDeletion;

  SlaThresholds({
    this.recordCreation,
    this.recordUpdate,
    this.recordSearch,
    this.recordDeletion,
  });

}

class VolumeMetrics {
  int? averageVolume;
  int? peakVolume;
  String? unit;

  VolumeMetrics({
    this.averageVolume,
    this.peakVolume,
    this.unit,
  });

}

class PerformanceMetadataResourcePatterns {
  HrManager? hrManager;

  PerformanceMetadataResourcePatterns({
    this.hrManager,
  });

}

class HrManager {
  String? activeHours;
  String? peakLoadPeriods;
  int? concurrentExecutions;

  HrManager({
    this.activeHours,
    this.peakLoadPeriods,
    this.concurrentExecutions,
  });

}

class Rule {
  String? id;
  String? ruleName;
  String? naturalLanguage;
  List<String>? ruleInputs;
  String? ruleOperation;
  String? ruleDescription;
  String? ruleOutput;
  String? ruleError;
  String? ruleValidation;

  Rule({
    this.id,
    this.ruleName,
    this.naturalLanguage,
    this.ruleInputs,
    this.ruleOperation,
    this.ruleDescription,
    this.ruleOutput,
    this.ruleError,
    this.ruleValidation,
  });

}

class ProcessFlow {
  String? loName;
  String? actorType;
  String? description;
  String? routeType;
  String? id;
  String? goId;
  String? loId;
  String? naturalLanguage;
  List<String>? routes;

  ProcessFlow({
    this.loName,
    this.actorType,
    this.description,
    this.routeType,
    this.id,
    this.goId,
    this.loId,
    this.naturalLanguage,
    this.routes,
  });

}

class ProcessMiningSchema {
  String? naturalLanguage;
  SchemaData? schemaData;
  ProcessMiningSchemaPerformanceDiscoveryMetrics? performanceDiscoveryMetrics;
  ProcessMiningSchemaConformanceAnalytics? conformanceAnalytics;
  ProcessMiningSchemaAdvancedProcessIntelligence? advancedProcessIntelligence;
  String? id;
  String? goId;

  ProcessMiningSchema({
    this.naturalLanguage,
    this.schemaData,
    this.performanceDiscoveryMetrics,
    this.conformanceAnalytics,
    this.advancedProcessIntelligence,
    this.id,
    this.goId,
  });

}

class ProcessMiningSchemaAdvancedProcessIntelligence {
  ProcessHealthScore? processHealthScore;
  OptimizationInsights? optimizationInsights;

  ProcessMiningSchemaAdvancedProcessIntelligence({
    this.processHealthScore,
    this.optimizationInsights,
  });

}

class ProcessMiningSchemaConformanceAnalytics {
  int? complianceRate;
  ExecutionVariance? executionVariance;
  FluffyExceptionPatterns? exceptionPatterns;

  ProcessMiningSchemaConformanceAnalytics({
    this.complianceRate,
    this.executionVariance,
    this.exceptionPatterns,
  });

}

class FluffyExceptionPatterns {
  CreateEmployeeRecordClass? validationFailure;

  FluffyExceptionPatterns({
    this.validationFailure,
  });

}

class CreateEmployeeRecordClass {
  CreateEmployeeRecordClass();
}

class ExecutionVariance {
  CreateEmployeeRecordClass? createEmployeeRecord;

  ExecutionVariance({
    this.createEmployeeRecord,
  });

}

class ProcessMiningSchemaPerformanceDiscoveryMetrics {
  PathwayFrequency? pathwayFrequency;
  ExecutionVariance? bottleneckAnalysis;
  PurpleResourcePatterns? resourcePatterns;

  ProcessMiningSchemaPerformanceDiscoveryMetrics({
    this.pathwayFrequency,
    this.bottleneckAnalysis,
    this.resourcePatterns,
  });

}

class PathwayFrequency {
  CreateEmployeeRecordClass? newEmployeeRegistration;

  PathwayFrequency({
    this.newEmployeeRegistration,
  });

}

class PurpleResourcePatterns {
  CreateEmployeeRecordClass? hrManager;

  PurpleResourcePatterns({
    this.hrManager,
  });

}

class SchemaData {
  EventLogSpecification? eventLogSpecification;

  SchemaData({
    this.eventLogSpecification,
  });

}

class EventLogSpecification {
  String? caseId;
  String? activity;
  String? eventType;
  String? timestamp;
  String? resource;
  String? duration;
  Attributes? attributes;

  EventLogSpecification({
    this.caseId,
    this.activity,
    this.eventType,
    this.timestamp,
    this.resource,
    this.duration,
    this.attributes,
  });

}

class Attributes {
  String? entityState;
  String? inputValues;
  String? outputValues;
  String? executionStatus;
  String? errorDetails;

  Attributes({
    this.entityState,
    this.inputValues,
    this.outputValues,
    this.executionStatus,
    this.errorDetails,
  });

}

class ProcessOwnership {
  String? naturalLanguage;
  String? originator;
  String? processOwner;
  String? businessSponsor;
  String? id;
  String? goId;

  ProcessOwnership({
    this.naturalLanguage,
    this.originator,
    this.processOwner,
    this.businessSponsor,
    this.id,
    this.goId,
  });

}

class RollbackPathway {
  String? id;
  String? goId;
  String? fromLo;
  String? toLo;
  String? pathwayType;
  String? naturalLanguage;

  RollbackPathway({
    this.id,
    this.goId,
    this.fromLo,
    this.toLo,
    this.pathwayType,
    this.naturalLanguage,
  });

}

class TriggerDefinition {
  String? naturalLanguage;
  String? triggerType;
  String? triggerCondition;
  String? triggerSchedule;
  List<String>? triggerAttributes;
  String? id;
  String? goId;

  TriggerDefinition({
    this.naturalLanguage,
    this.triggerType,
    this.triggerCondition,
    this.triggerSchedule,
    this.triggerAttributes,
    this.id,
    this.goId,
  });

}
