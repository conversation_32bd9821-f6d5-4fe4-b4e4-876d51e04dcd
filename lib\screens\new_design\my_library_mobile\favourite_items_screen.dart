import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';

class FavouriteItemsScreen extends StatefulWidget {
  final List<Map<String, dynamic>> allData;
  final Function(List<Map<String, dynamic>>)? onFavoriteChanged;

  const FavouriteItemsScreen({
    Key? key,
    required this.allData,
    this.onFavoriteChanged,
  }) : super(key: key);

  @override
  State<FavouriteItemsScreen> createState() => _FavouriteItemsScreenState();
}

class _FavouriteItemsScreenState extends State<FavouriteItemsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showSearchBar = false;
  bool _hasShownSearchBarOnce = false;

  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  int _currentPage = 0;
  final int _itemsPerPage = 10;
  String? _selectedCategoryFilter;
  int? _hoveredRowIndex;
  bool _isSearchActive = false;

  List<Map<String, dynamic>> get _filteredData {
    // Start with only favorite items
    List<Map<String, dynamic>> filtered =
        widget.allData.where((item) => item['isFavorite'] == true).toList();

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) {
        final fileName = item['fileName'].toString().toLowerCase();
        final type = item['type'].toString().toLowerCase();
        final query = _searchQuery.toLowerCase();
        return fileName.contains(query) || type.contains(query);
      }).toList();
    }

    // Apply category filter
    if (_selectedCategoryFilter != null) {
      filtered = filtered
          .where((item) => item['type'] == _selectedCategoryFilter)
          .toList();
    }

    // Sort the data by last opened (most recent first)
    filtered.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return filtered;
  }

  List<Map<String, dynamic>> get _paginatedData {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, _filteredData.length);
    return _filteredData.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredData.length / _itemsPerPage).ceil();

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(() {
      if (_scrollController.position.userScrollDirection ==
          ScrollDirection.forward) {
        // Show search bar ONCE
        if (!_hasShownSearchBarOnce) {
          setState(() {
            _showSearchBar = true;
            _hasShownSearchBarOnce = true;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // Show sort bottom sheet
  void _showSortBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.only(top: 20, bottom: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Sort by',
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Icon(Icons.close, color: Colors.black, size: 24),
                      ),
                    ],
                  ),
                ),

                Divider(height: 1, color: Colors.grey.shade300),

                _buildSortOption('Projects', 'project'),
                Divider(height: 1, color: Colors.grey.shade300),
                _buildSortOption('Solutions', 'solution'),
                Divider(height: 1, color: Colors.grey.shade300),
                _buildSortOption('Objects', 'object'),
                Divider(height: 1, color: Colors.grey.shade300),
                _buildSortOption('Roles/Agent', 'role'),
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSortOption(String title, String column) {
    final category = _getCategoryFromColumn(column);

    String svgPath;
    switch (column) {
      case 'project':
        svgPath = 'assets/images/my_library/create_project_mobile.svg';
        break;
      case 'solution':
        svgPath = 'assets/images/my_library/create_solution_mobile.svg';
        break;
      case 'object':
        svgPath = 'assets/images/my_library/create_object_mobile.svg';
        break;
      case 'role':
        svgPath = 'assets/images/my_library/create_roles_mobile.svg';
        break;
      default:
        svgPath = 'assets/images/my_library/my_library_discover.svg';
    }
    return GestureDetector(
      onTap: () {
        _filterByCategory(column);
        Navigator.pop(context);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            SvgPicture.asset(
              svgPath,
              width: 14,
              height: 14,
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String? _getCategoryFromColumn(String column) {
    switch (column) {
      case 'project':
        return 'Project';
      case 'solution':
        return 'Solution';
      case 'object':
        return 'Object';
      case 'role':
        return 'Role';
      default:
        return null;
    }
  }

  void _filterByCategory(String column) {
    final category = _getCategoryFromColumn(column);
    setState(() {
      if (_selectedCategoryFilter == category) {
        // If already selected, clear the filter
        _selectedCategoryFilter = null;
      } else {
        // Set new filter
        _selectedCategoryFilter = category;
      }
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          icon: SvgPicture.asset(
            'assets/images/my_library/create_back.svg',
            width: 34,
            height: 34,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Favourite',
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
        backgroundColor: Color(0xffF7F9FB),
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
        actions: [
          // Filter icon only
          IconButton(
            icon: SvgPicture.asset(
              'assets/images/my_library/create_sort.svg',
              width: 20,
              height: 20,
            ),
            onPressed: _showSortBottomSheet,
          ),
        ],
      ),
      body: Column(
        children: [
          // Always visible search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildSearchBar(),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: Column(
                        children: [
                          _buildTable(),
                          const SizedBox(height: 16),
                          _buildPagination(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppSpacing.xs),
        border: Border.all(color: Colors.grey.shade300, width: 0.5),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: _searchController,
              cursorHeight: 16,
              autofocus: false,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _currentPage = 0;
                });
              },
              decoration: InputDecoration(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0),
                isDense: true,
                hintText: 'Search',
                hintStyle: TextStyle(
                  color: Color(0xffD0D0D0),
                  fontSize: 14,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              style: TextStyle(
                fontSize: 14,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              onSubmitted: (value) {
                FocusScope.of(context).unfocus();
              },
            ),
          ),

          // Search icon
          Padding(
            padding: const EdgeInsets.only(right: 12.0),
            child: Icon(
              Icons.search,
              size: 20,
              color: Colors.grey.shade400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        // border: Border.all(color: Color(0xffE6EAEE), width: 0.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // _buildTableHeader(),
          ListView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: _paginatedData.length,
            itemBuilder: (context, index) {
              return _buildTableRow(_paginatedData[index], index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
          top: BorderSide(color: Color(0xffD0D0D0), width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // File Name - Left aligned
          Expanded(
            flex: 4,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'File Name',
                style: TextStyle(
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              ),
            ),
          ),
          // Last Opened
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Last Opened',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          // Favorites column - empty header for star icon space
          Expanded(
            flex: 1,
            child: Container(),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(Map<String, dynamic> item, int index) {
    final isEvenRow = index % 2 == 0;
    final isHovered = _hoveredRowIndex == index;
    final isFavorite = item['isFavorite'] ?? false;

    return MouseRegion(
      onEnter: (_) => setState(() => _hoveredRowIndex = index),
      onExit: (_) => setState(() => _hoveredRowIndex = null),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isEvenRow ? Colors.white : Colors.white,
          border: Border(
            bottom: BorderSide(color: Color(0xffE6EAEE), width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // Left icon section
            _buildTypeIcon(item['type']),
            const SizedBox(width: 12),

            // Middle section - File name and type with date
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item['fileName'],
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimaryLight,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Row(
                    children: [
                      if (item['type'].isNotEmpty)
                        Text(
                          item['type'],
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: AppColors.black,
                          ),
                        ),
                      if (item['type'].isNotEmpty)
                        Text(
                          ' | ',
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textGreyColor,
                          ),
                        ),
                      Text(
                        _formatDate(item['lastOpened']),
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                          color: AppColors.textGreyColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Right section - Only star icon
            GestureDetector(
              onTap: () {
                setState(() {
                  item['isFavorite'] = !item['isFavorite'];
                });
                // Call the callback to update the parent screen
                if (widget.onFavoriteChanged != null) {
                  widget.onFavoriteChanged!(widget.allData);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(8.0), // Larger tap area
                child: Icon(
                  item['isFavorite'] ? Icons.star : Icons.star_border,
                  size: 24, // Slightly larger for mobile
                  color:
                      item['isFavorite'] ? Colors.amber : Colors.grey.shade400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeIcon(String type) {
    String svgAsset;

    switch (type) {
      case 'Role':
        svgAsset = 'assets/images/my_library/create_role.svg';
        break;
      case 'Object':
        svgAsset = 'assets/images/my_library/create_object.svg';
        break;
      case 'Solution':
        svgAsset = 'assets/images/my_library/create_solution.svg';
        break;
      case 'Project':
        svgAsset = 'assets/images/my_library/create_project.svg';
        break;
      default:
        svgAsset = 'assets/icons/default.svg';
    }

    return Center(
      child: CustomImage.asset(
        svgAsset,
        width: 32,
        height: 32,
        fit: BoxFit.contain,
      ).toWidget(),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday, ${date.day}/${date.month}/${date.year}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildPagination() {
    if (_totalPages <= 1) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppSpacing.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Navigation buttons
          Row(
            children: [
              // Previous button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_left, size: 20),
                onPressed: _currentPage > 0
                    ? () {
                        setState(() {
                          _currentPage--;
                        });
                      }
                    : null,
              ),
              const SizedBox(width: 8),
              // Next button
              _HoverPaginationButton(
                icon: const Icon(Icons.chevron_right, size: 20),
                onPressed: _currentPage < _totalPages - 1
                    ? () {
                        setState(() {
                          _currentPage++;
                        });
                      }
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
