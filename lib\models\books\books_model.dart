// To parse this JSON data, do
//
//     final booksResponse = booksResponseFromJson(jsonString);

import 'dart:convert';

BooksResponse booksResponseFromJson(String str) => BooksResponse.fromJson(json.decode(str));

String booksResponseToJson(BooksResponse data) => json.encode(data.toJson());

class BooksResponse {
    List<BooksModel>? books;
    int? totalCount;
    int? filteredCount;

    BooksResponse({
        this.books,
        this.totalCount,
        this.filteredCount,
    });

    BooksResponse copyWith({
        List<BooksModel>? books,
        int? totalCount,
        int? filteredCount,
    }) => 
        BooksResponse(
            books: books ?? this.books,
            totalCount: totalCount ?? this.totalCount,
            filteredCount: filteredCount ?? this.filteredCount,
        );

    factory BooksResponse.fromJson(Map<String, dynamic> json) => BooksResponse(
        books: json["books"] == null ? [] : List<BooksModel>.from(json["books"]!.map((x) => BooksModel.fromJson(x))),
        totalCount: json["total_count"],
        filteredCount: json["filtered_count"],
    );

    Map<String, dynamic> toJson() => {
        "books": books == null ? [] : List<dynamic>.from(books!.map((x) => x.toJson())),
        "total_count": totalCount,
        "filtered_count": filteredCount,
    };
}

class BooksModel {
    int? id;
    String? name;
    String? description;
    String? tenantId;
    DateTime? createdAt;
    String? createdBy;
    DateTime? updatedAt;
    String? updatedBy;
    String? image;

    BooksModel({
        this.id,
        this.name,
        this.description,
        this.tenantId,
        this.createdAt,
        this.createdBy,
        this.updatedAt,
        this.updatedBy,
        this.image,
        // Backward compatibility parameters
        String? bookId,
        String? bookName,
        int? objectiveCount,
    }) {
        // Handle backward compatibility
        if (bookId != null && id == null) {
            id = int.tryParse(bookId);
        }
        if (bookName != null && name == null) {
            name = bookName;
        }
    }

    // Getter for backward compatibility
    String? get bookId => id?.toString();
    String? get bookName => name;
    int? get objectiveCount => 0; // Default value for backward compatibility

    BooksModel copyWith({
        int? id,
        String? name,
        String? description,
        String? tenantId,
        DateTime? createdAt,
        String? createdBy,
        DateTime? updatedAt,
        String? updatedBy,
        String? image,
    }) => 
        BooksModel(
            id: id ?? this.id,
            name: name ?? this.name,
            description: description ?? this.description,
            tenantId: tenantId ?? this.tenantId,
            createdAt: createdAt ?? this.createdAt,
            createdBy: createdBy ?? this.createdBy,
            updatedAt: updatedAt ?? this.updatedAt,
            updatedBy: updatedBy ?? this.updatedBy,
            image: image ?? this.image,
        );

    factory BooksModel.fromJson(Map<String, dynamic> json) => BooksModel(
        id: json["id"],
        name: json["name"],
        description: json["description"],
        tenantId: json["tenant_id"],
        createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
        createdBy: json["created_by"],
        updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
        updatedBy: json["updated_by"],
        image: json["image"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "description": description,
        "tenant_id": tenantId,
        "created_at": createdAt?.toIso8601String(),
        "created_by": createdBy,
        "updated_at": updatedAt?.toIso8601String(),
        "updated_by": updatedBy,
        "image": image,
    };
}
