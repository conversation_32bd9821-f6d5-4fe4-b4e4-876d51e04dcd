import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kDebugMode, kIsWeb;
import 'package:flutter_svg/svg.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/auth_provider.dart';
import 'package:nsl/providers/language_provider.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/new_design/widgets/custom_alert_dialog.dart';
import 'package:nsl/screens/web_transaction/web_collection_module_widgets.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/navigation_service.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';

// Global registry to track active business submenu overlay
OverlayEntry? activeBusinessSubmenu;

// Global function to close business submenu
void closeBusinessSubmenu() {
  try {
    if (activeBusinessSubmenu != null) {
      activeBusinessSubmenu!.remove();
      activeBusinessSubmenu = null;
    }
  } catch (e) {
    // Safely handle any errors that might occur when removing the overlay
    print('Error closing business submenu: $e');
    activeBusinessSubmenu = null;
  }
}

class Sidebar extends StatefulWidget {
  const Sidebar({
    super.key,
  });

  @override
  State<Sidebar> createState() => _SidebarState();
}

class _SidebarState extends State<Sidebar> {
  // Track if language menu is active
  bool isLanguageMenuActive = false;

  // Map of language names to language codes with country codes
  final Map<String, Map<String, String>> languageCodes = {
    "English": {"code": "en", "country": "US"},
    "Spanish": {"code": "es", "country": "ES"},
    "French": {"code": "fr", "country": "FR"},
    "German": {"code": "de", "country": "DE"},
    "Chinese": {"code": "zh", "country": "CN"},
    "Japanese": {"code": "ja", "country": "JP"},
    "Korean": {"code": "ko", "country": "KR"},
    "Russian": {"code": "ru", "country": "RU"},
    "Arabic": {"code": "ar", "country": "SA"},
    "Hindi": {"code": "hi", "country": "IN"},
    "Telugu": {"code": "te", "country": "IN"},
  };

  // Get language code from language name
  Map<String, String> _getLanguageInfo(String language) {
    return languageCodes[language] ??
        {"code": "en", "country": "US"}; // Default to English if not found
  }

  @override
  Widget build(BuildContext econtext) {
    final provider = Provider.of<WebHomeProvider>(context);

    // Define sidebar items with labels for tooltips and screen names
    final List<Map<String, dynamic>> sidebarItems = [
      {
        "icon": "assets/images/sidebar_icons/chat.svg",
        "label": AppLocalizations.of(context).translate('sidemenu.chat'),
        "screen": ScreenConstants.home
      },
      // {
      //   "icon": "assets/images/sidebar_icons/create.svg",
      //   "label": AppLocalizations.of(context).translate('sidemenu.create'),
      //   "screen": ScreenConstants.create
      // },
      {
        "icon": "assets/images/sidebar_icons/create.svg",
        "label": AppLocalizations.of(context).translate('sidemenu.create'),
        "screen": ScreenConstants
            .webCreationFlowMainScreen // Replace with the correct getter/field name
      },

      {
        "icon": "assets/images/sidebar_icons/business.svg",
        "label": AppLocalizations.of(context).translate('sidemenu.myBusiness'),
        "screen": '',
        "hasSubItems": true,
        "subItems": [
          {
            "icon": "assets/images/my_business/home_business.svg",
            "label": AppLocalizations.of(context).translate('sidemenu.home'),
            "screen": ScreenConstants.myBusinessHome
          },
          {
            "icon": "assets/images/my_business/collection_business.svg",
            "label":
                AppLocalizations.of(context).translate('sidemenu.collections'),
            "screen": ScreenConstants.myBusinessCollections
          },
          {
            "icon": "assets/images/my_business/solutions_business.svg",
            "label":
                AppLocalizations.of(context).translate('sidemenu.solutions'),
            "screen": ScreenConstants.myBusinessSolutions
          },
          {
            "icon": "assets/images/my_business/records_business.svg",
            "label": AppLocalizations.of(context).translate('sidemenu.records'),
            "screen": ScreenConstants.myBusinessRecords
          }
        ]
      },
      // {"icon": "assets/images/sidebar_icons/design.svg", "label": "Design", "screen": "design"},
      {
        "icon": "assets/images/sidebar_icons/my_transaction.svg",
        "label":
            AppLocalizations.of(context).translate('sidemenu.myTransactions'),
        "screen": ScreenConstants.myTransactions
      },
      // {
      //   "icon": "assets/images/sidebar_icons/calendar.svg",
      //   "label": AppLocalizations.of(context).translate('sidemenu.calendar'),
      //   "screen": ScreenConstants.calendar
      // },
      // {
      //   "icon": "assets/images/sidebar_icons/notifications.svg",
      //   "label":
      //       AppLocalizations.of(context).translate('sidemenu.notifications'),
      //   "screen": ScreenConstants.notifications
      // },
      {
        "icon": "assets/images/sidebar_icons/code 1.svg",
        "label":
            AppLocalizations.of(context).translate('sidemenu.nslToJavaCode'),
        "screen": ScreenConstants.nslJavaSolutionsScreen
      },
      {
        "icon": "assets/images/sidebar_icons/ocr.svg",
        "label": AppLocalizations.of(context).translate('sidemenu.multiMedia'),
        "screen": ScreenConstants.tempWebChat
      },
      {
        "icon": "assets/images/sidebar_icons/instant-financial.svg",
        "label":
            AppLocalizations.of(context).translate('sidemenu.instantFinancial'),
        "screen": ScreenConstants.treeModel1
      },
      //  {
      //   "icon": "assets/images/sidebar_icons/ocr.svg",
      //   "label": AppLocalizations.of(context).translate('sidemenu.nslToJavaParsing'),
      //   "screen": ScreenConstants.nslJavaContentParsing
      // },

      //  {
      //   "icon": "assets/images/sidebar_icons/my_transaction.svg",
      //   "label":
      //       AppLocalizations.of(context).translate('sidemenu.myTransactions'),
      //   "screen": ScreenConstants.dashboardWidgets
      // },
    ];

    return Container(
      height: MediaQuery.of(context).size.height,
      width: 50,
      decoration: BoxDecoration(
          color: Color(0xffF7F9FB),
          border: Border(
            right: BorderSide(color: Color(0xffD0D0D0), width: 1),
          )
          // border: Border.all(color: Color(0xffD0D0D0), width: 1)

          ),
      padding: EdgeInsets.zero, // Remove any default padding
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Logo with no padding
                  SizedBox(
                    height: AppSpacing.xxs,
                  ),
                  Container(
                    padding: EdgeInsets.zero,
                    margin: EdgeInsets.zero,
                    child: MouseRegion(
                      cursor: SystemMouseCursors.click,
                      onEnter: (_) {
                        // Close business submenu when hovering over logo
                        closeBusinessSubmenu();
                      },
                      child: GestureDetector(
                          onTap: () async {
                            // Store the current screen index

                            provider.currentScreenIndex = ScreenConstants.home;
                            provider.isChatHistoryExpanded = false;
                            // First reset the conversation
                            await provider.resetConversation();

                            // // Only change to home screen if we're not already on a screen
                            // // This preserves the current screen during layout changes
                            // final currentIndex = provider.currentScreenIndex;
                            // if (currentIndex.isEmpty) {
                            //   provider.currentScreenIndex = ScreenConstants.home;
                            //   Logger.info('Logo clicked - Setting screen index to home');
                            // } else {
                            //   Logger.info('Logo clicked - Preserving screen index: $currentIndex');
                            // }
                          },
                          child: SvgPicture.asset(
                            'assets/images/login_logo.svg',
                            width: 40,
                            height: 40,
                            fit: BoxFit.contain,
                          )),
                    ),
                  ),
                  // Sidebar items
                  Column(
                    children: sidebarItems.asMap().entries.map((entry) {
                      final item = entry.value;
                      final String screenName = item["screen"]!;
                      final bool hasSubItems = item["hasSubItems"] == true;

                      if (hasSubItems) {
                        return _SidebarItemWithSubItems(
                          iconPath: item["icon"]!,
                          label: item["label"]!,
                          subItems: item["subItems"],
                          isSelected: provider.currentScreenIndex == screenName,
                          onTap: () async {
                            if (screenName.isNotEmpty) {
                              // Add your action here when icon is clicked
                              provider.currentScreenIndex = screenName;
                              await provider.resetConversation();

                              // Update chat history expanded state
                              if (screenName == ScreenConstants.home) {
                                provider.isChatHistoryExpanded = true;
                                await provider.fetchChatHistory();
                              } else {
                                provider.isChatHistoryExpanded = false;
                              }
                            }
                          },
                          onSubItemTap: (subItem) async {
                            // Handle navigation for sub-items
                            final String subScreenName = subItem["screen"]!;
                            provider.currentScreenIndex = subScreenName;

                            // Update chat history expanded state
                            if (subScreenName == ScreenConstants.home) {
                              provider.isChatHistoryExpanded = true;
                              await provider.fetchChatHistory();
                            } else {
                              provider.isChatHistoryExpanded = false;
                            }
                            await provider.resetConversation();
                          },
                        );
                      } else {
                        return _SidebarItem(
                          iconPath: item["icon"]!,
                          label: item["label"]!,
                          isSelected: provider.currentScreenIndex == screenName,
                          onTap: () async {
                            // Add your action here when icon is clicked
                            provider.currentScreenIndex = screenName;

                            // Update chat history expanded state
                            if (screenName == ScreenConstants.home) {
                              if (!provider.isChatHistoryExpanded) {
                                provider.isChatHistoryExpanded = true;
                                await provider.fetchChatHistory();
                              }
                            } else if (screenName == ScreenConstants.create) {
                              final myProvider =
                                  context.read<WebHomeProviderStatic?>();
                              if (myProvider != null) {
                                // myProvider.isChatHistoryExpanded = true;
                                // await myProvider.fetchCreateChatHistory();
                                if (kDebugMode) {
                                  print('Provider is available');
                                }
                                // myProvider.clearMessages();
                                // myProvider.currentSessionId = null;
                                myProvider.resetConversation();
                              } else {
                                if (kDebugMode) {
                                  print('Provider not found');
                                }
                              }
                            } else {
                              provider.isChatHistoryExpanded = false;
                            }
                            if (screenName != ScreenConstants.home) {
                              await provider.resetConversation();
                            } else if (screenName != ScreenConstants.create) {
                              Provider.of<WebHomeProviderStatic>(context,
                                      listen: false)
                                  .resetConversation();
                            }
                          },
                        );
                      }
                    }).toList(),
                  )
                ],
              ),
            ),
          ),
          // Spacer(),
          // User profile icon
          _SidebarItem(
            iconPath: "assets/images/sidebar_icons/user.svg",
            label: AppLocalizations.of(context).translate('sidemenu.myProfile'),
            onTap: () {
              _showProfileDropdown(context);
            },
          )
        ],
      ),
    );
  }

  void _showProfileDropdown(BuildContext context) {
    showDialog(
        context: context,
        barrierColor: Colors.transparent,
        builder: (BuildContext context) {
          return Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              final user = authProvider.user;
              final userEmail = user?.email ?? 'No email available';
              final userInitials = user?.initials ?? 'U';
              // final String firstName = authProvider.user?.name != null
              //     ? (authProvider.user?.name ?? "").capitalize() ?? ''
              //     : "";
              // final String lastName = authProvider.user?.name != null
              //     ? (authProvider.user?.name ?? "").capitalize() ?? ''
              //     : "";

              return Stack(
                children: [
                  Positioned(
                    left: 51,
                    bottom: 1,
                    child: Material(
                      color: Colors.transparent,
                      child: Container(
                        padding: EdgeInsets.only(bottom: 16),
                        width: 190,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(6),
                          // border: Border.all(color: Color(0xFFD0D0D0), width: 1),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(25),
                              blurRadius: 6,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // User info section
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // User email with circle avatar
                                  Row(
                                    children: [
                                      Container(
                                        width: 32,
                                        height: 32,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.grey.shade200,
                                          border: Border.all(
                                              color: Colors.grey.shade300,
                                              width: 1),
                                        ),
                                        child: Center(
                                          child: Text(
                                            userInitials,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade700,
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 6),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            authProvider
                                                    .user?.formattedFullName ??
                                                'User Name',
                                            style: TextStyle(
                                              fontSize: 12,
                                              // fontWeight: FontWeight.bold,
                                              fontFamily: 'TiemposText',
                                              color: Colors.black,
                                            ),
                                          ),
                                          Text(
                                            userEmail,
                                            style: TextStyle(
                                              fontSize: 10,
                                              fontFamily: 'TiemposText',
                                              color: Colors.grey.shade700,
                                            ),
                                            // overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 14),
                                  // Max new plan
                                  Row(
                                    children: [
                                      SvgPicture.asset(
                                        "assets/images/sidebar_icons/plan.svg",
                                        width: 18,
                                        height: 18,
                                        // colorFilter: ColorFilter.mode(
                                        //   Color(0xff5a5b5b),
                                        //   BlendMode.srcIn,
                                        // ),
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        AppLocalizations.of(context)
                                            .translate('sidemenu.maxNewPlan'),
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xff000000),
                                          fontWeight: FontWeight.w600,
                                          fontFamily: 'TiemposText',
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 8),
                                ],
                              ),
                            ),
                            Divider(height: 1, color: Color(0xFFD0D0D0)),
                            SizedBox(height: 4),
                            _buildMenuItem(
                              context,
                              'NSL-RAG-System',
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.nslRag;
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              'TreeModel',
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.treeModel;
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              'Tree Model New',
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.treeModelNew;
                                Navigator.of(context).pop();
                              },
                            ),
                            // Menu items
                            _buildMenuItem(
                              context,
                              'Static flow',
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Provider.of<WebHomeProviderStatic>(context,
                                        listen: false)
                                    .resetConversation();
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.webHomeStatic;

                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              'Object Static flow',
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Provider.of<WebHomeProviderStatic>(context,
                                        listen: false)
                                    .resetConversation();
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.webMyObject;

                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              'collection',
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.webCollectionModuleDemo;
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              'demo',
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Provider.of<WebHomeProvider>(context,
                                            listen: false)
                                        .currentScreenIndex =
                                    ScreenConstants.webTransactionWidgetsDemo;
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              AppLocalizations.of(context)
                                  .translate('sidemenu.settings'),
                              "assets/images/sidebar_icons/settings.svg",
                              () {
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              AppLocalizations.of(context)
                                  .translate('sidemenu.viewPlan'),
                              "assets/images/sidebar_icons/view_plan.svg",
                              () {
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              AppLocalizations.of(context)
                                  .translate('sidemenu.learnMore'),
                              "assets/images/sidebar_icons/learn_more.svg",
                              () {
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildLanguageMenuItem(context),
                            _buildMenuItem(
                              context,
                              AppLocalizations.of(context)
                                  .translate('sidemenu.getHelp'),
                              "assets/images/sidebar_icons/help.svg",
                              () {
                                Navigator.of(context).pop();
                              },
                            ),
                            _buildMenuItem(
                              context,
                              AppLocalizations.of(context)
                                  .translate('sidemenu.logout'),
                              "assets/images/sidebar_icons/logout.svg",
                              () {
                                // Close the sidebar menu first
                                Navigator.of(context).pop();
                                // Show logout confirmation dialog
                                _showLogoutConfirmation(context);
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        });
  }

  // Helper method to build menu items
  Widget _buildMenuItem(
      BuildContext context, String label, String iconPath, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            SvgPicture.asset(
              iconPath,
              width: 18,
              height: 18,
              // colorFilter: ColorFilter.mode(
              //   Color(0xff5a5b5b),
              //   BlendMode.srcIn,
              // ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xff000000),
                  fontWeight: FontWeight.w500,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Show logout confirmation dialog
  void _showLogoutConfirmation(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    // Store the scaffold messenger and theme for later use
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final errorColor = Theme.of(context).colorScheme.error;
    final cancelText = AppLocalizations.of(context).translate('common.cancel');
    final okText = AppLocalizations.of(context).translate('common.ok');
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) => CustomAlertDialog(
          title: 'sidemenu.logout',
          content: 'navigation.logoutConfirmation',
          onClose: () => Navigator.of(dialogContext).pop(),
          primaryButtonText: 'common.ok',
          onPrimaryPressed: () async {
            Navigator.of(dialogContext).pop(); // Close the dialog

            try {
              // Perform logout
              final success = await authProvider.logout();

              // Check if widget is still mounted before using context
              if (mounted) {
                if (success) {
                  // Provider.of<WebHomeProvider>(context, listen: false).clear();
                  // authProvider.clearProvider(context);
                  // Navigate to login screen
                  NavigationService.navigateToLogin();
                } else {
                  // Show error if logout failed
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(authProvider.error ?? 'Logout failed'),
                      backgroundColor: errorColor,
                    ),
                  );
                }
              }
            } catch (e, stackTrace) {
              // Check if widget is still mounted before using context
              if (mounted) {
                print(stackTrace);
                // Show error if exception occurred
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('Error during logout: $e'),
                    backgroundColor: errorColor,
                  ),
                );
              }
            }
          },
          secondaryButtonText: 'common.cancel',
          onSecondaryPressed: () => Navigator.of(dialogContext).pop()),
    );

    //   showDialog(
    //     context: context,
    //     builder: (BuildContext dialogContext) {
    //       return AlertDialog(
    //         title: Row(
    //           children: [
    //             Icon(Icons.logout, color: errorColor),
    //             const SizedBox(width: 8),
    //             Text(
    //               AppLocalizations.of(dialogContext).translate('sidemenu.logout'),
    //               style: TextStyle(
    //                 fontSize: 20,
    //                 fontWeight: FontWeight.w500,
    //                 fontFamily: 'TiemposText',
    //               ),
    //             ),
    //           ],
    //         ),
    //         content: Text(
    //           AppLocalizations.of(dialogContext)
    //               .translate('navigation.logoutConfirmation'),
    //           style: TextStyle(
    //             fontSize: 16,
    //             fontWeight: FontWeight.w400,
    //             fontFamily: 'TiemposText',
    //           ),
    //         ),
    //         actions: [
    //           TextButton(
    //             onPressed: () {
    //               Navigator.of(dialogContext).pop(); // Close the dialog
    //             },
    //             child: Text(
    //               cancelText,
    //               style: TextStyle(
    //                 fontFamily: 'TiemposText',
    //               ),
    //             ),
    //           ),
    //           TextButton(
    //             onPressed: () async {
    //               Navigator.of(dialogContext).pop(); // Close the dialog

    //               try {
    //                 // Perform logout
    //                 final success = await authProvider.logout();

    //                 // Check if widget is still mounted before using context
    //                 if (mounted) {
    //                   if (success) {
    //                     // Navigate to login screen
    //                     NavigationService.navigateToLogin();
    //                   } else {
    //                     // Show error if logout failed
    //                     scaffoldMessenger.showSnackBar(
    //                       SnackBar(
    //                         content: Text(authProvider.error ?? 'Logout failed'),
    //                         backgroundColor: errorColor,
    //                       ),
    //                     );
    //                   }
    //                 }
    //               } catch (e) {
    //                 // Check if widget is still mounted before using context
    //                 if (mounted) {
    //                   // Show error if exception occurred
    //                   scaffoldMessenger.showSnackBar(
    //                     SnackBar(
    //                       content: Text('Error during logout: $e'),
    //                       backgroundColor: errorColor,
    //                     ),
    //                   );
    //                 }
    //               }
    //             },
    //             style: TextButton.styleFrom(
    //               foregroundColor: errorColor,
    //             ),
    //             child: Text(
    //               okText,
    //               style: TextStyle(
    //                 fontFamily: 'TiemposText',
    //               ),
    //             ),
    //           ),
    //         ],
    //       );
    //     },
    //   );
  }

  // Language menu item with submenu
  Widget _buildLanguageMenuItem(BuildContext context) {
    return InkWell(
      onTap: () {
        // Toggle language menu active state
        setState(() {
          isLanguageMenuActive = true;
        });
        // Show language dialog
        _showLanguageDialog(context);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        color: isLanguageMenuActive
            ? Color(0xff0058FF).withValues(alpha: 0.1 * 255)
            : Colors.transparent,
        child: Row(
          children: [
            SvgPicture.asset(
              "assets/images/sidebar_icons/language.svg",
              width: 16,
              height: 16,
              colorFilter: ColorFilter.mode(
                Color(0xff5a5b5b),
                BlendMode.srcIn,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                AppLocalizations.of(context).translate('sidemenu.language'),
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xff000000),
                  fontWeight: FontWeight.w500,
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 12,
              color: Colors.grey.shade700,
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    // Show the dialog and handle dismissal
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      barrierDismissible: true,
      builder: (BuildContext context) {
        // Add a listener to reset state when dialog is closed
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!context.mounted) return;
          // When dialog is dismissed, update state
          Future.delayed(Duration(milliseconds: 100), () {
            if (!mounted) return;
            setState(() {
              isLanguageMenuActive = false;
            });
          });
        });
        return Stack(
          children: [
            Positioned(
              left: 233,
              bottom: 70,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  padding: EdgeInsets.only(top: 10, bottom: 10, left: 10),
                  width: 150,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(6),
                    // border: Border.all(color: Color(0xFFD0D0D0), width: 1),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: 6,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Builder(
                    builder: (context) {
                      // Get the current locale from the language provider
                      final languageProvider =
                          Provider.of<LanguageProvider>(context);
                      final currentLocale = languageProvider.locale;
                      final currentLangCode = currentLocale.languageCode;

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildLanguageOption(
                              "English", currentLangCode == 'en'),
                          _buildLanguageOption(
                              "Spanish", currentLangCode == 'es'),
                          _buildLanguageOption(
                              "French", currentLangCode == 'fr'),
                          _buildLanguageOption(
                              "German", currentLangCode == 'de'),
                          _buildLanguageOption(
                              "Chinese", currentLangCode == 'zh'),
                          _buildLanguageOption(
                              "Japanese", currentLangCode == 'ja'),
                          _buildLanguageOption(
                              "Korean", currentLangCode == 'ko'),
                          _buildLanguageOption(
                              "Russian", currentLangCode == 'ru'),
                          _buildLanguageOption(
                              "Arabic", currentLangCode == 'ar'),
                          _buildLanguageOption(
                              "Hindi", currentLangCode == 'hi'),
                          _buildLanguageOption(
                              "Telugu", currentLangCode == 'te'),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Language option item
  Widget _buildLanguageOption(String language, bool isSelected) {
    return InkWell(
      onTap: () {
        // Handle language selection with language code and country code
        final languageInfo = _getLanguageInfo(language);
        final code = languageInfo["code"]!;
        final country = languageInfo["country"]!;

        // Use the language code for your application
        debugPrint(
            'Selected language: $language with code: $code, country: $country');

        // Reset language menu active state
        setState(() {
          isLanguageMenuActive = false;
        });

        // Get the language provider and set the locale with country code
        final LanguageProvider languageProvider =
            Provider.of<LanguageProvider>(context, listen: false);
        languageProvider.setLocale(Locale(code, country));
        // Close dialog
        Navigator.of(context).pop();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 5),
        // color: isSelected ? Color(0xFFffffff) : Colors.white
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              language,
              style: TextStyle(
                fontSize: 12,
                fontFamily: 'TiemposText',
                color: isSelected ? Color(0xff000000) : Colors.black,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check,
                color: Color(0xff0058FF),
                size: 16,
              ),
          ],
        ),
      ),
    );
  }
}

class _SidebarItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;
  final bool isSelected;

  const _SidebarItem({
    required this.iconPath,
    required this.label,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  State<_SidebarItem> createState() => _SidebarItemState();
}

class _SidebarItemState extends State<_SidebarItem> {
  bool isHovered = false;
  final GlobalKey _iconKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    try {
      _removeTooltip();

      // Close any active business submenu when hovering over other sidebar items
      closeBusinessSubmenu();

      // Get the position of the icon
      final RenderBox? renderBox =
          _iconKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;

      _overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          left: position.dx + size.width,
          top: position.dy + (size.height / 2) - 18, // Center vertically
          child: Material(
            color: Colors.transparent,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(2),
              ),
              padding: EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              child: Text(
                widget.label,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      );

      // Check if the context is still valid before inserting the overlay
      if (!mounted) return;

      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
      // Safely handle any errors that might occur when showing the tooltip
      print('Error showing tooltip: $e');
      _overlayEntry = null;
    }
  }

  void _removeTooltip() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry!.remove();
        _overlayEntry = null;
      }
    } catch (e) {
      // Safely handle any errors that might occur when removing the overlay
      print('Error removing tooltip: $e');
      _overlayEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xxs),
      child: MouseRegion(
        onEnter: (_) {
          setState(() => isHovered = true);
          _showTooltip();
        },
        onExit: (_) {
          setState(() => isHovered = false);
          _removeTooltip();
        },
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
            key: _iconKey,
            decoration: widget.isSelected
                ? BoxDecoration(
                    color:
                        Colors.blue.withValues(alpha: 0.05) // 0.05 * 255 = ~13
                    )
                : BoxDecoration(
                    color: isHovered
                        ? Colors.blue
                            .withValues(alpha: 0.05) // 0.05 * 255 = ~13
                        : Colors.transparent,
                  ),
            child: Center(
              child: SvgPicture.asset(
                widget.iconPath,
                //  height: 20,
                // Change SVG color when selected or hovered
                colorFilter: widget.isSelected
                    ? ColorFilter.mode(Color(0xff0058FF), BlendMode.srcIn)
                    : isHovered
                        ? ColorFilter.mode(Color(0xff0058FF), BlendMode.srcIn)
                        : null,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Sub-menu item widget
class _SubMenuItem extends StatefulWidget {
  final String iconPath;
  final String label;
  final VoidCallback onTap;

  const _SubMenuItem({
    required this.iconPath,
    required this.label,
    required this.onTap,
  });

  @override
  State<_SubMenuItem> createState() => _SubMenuItemState();
}

class _SubMenuItemState extends State<_SubMenuItem> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: isHovered ? Color(0xffF6F6F6) : Colors.transparent,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                widget.iconPath,
                height: 18,
                width: 18,
                colorFilter: ColorFilter.mode(
                  isHovered ? Color(0xff0058FF) : Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              SizedBox(height: 3),
              Text(
                widget.label,
                style: TextStyle(
                  fontSize: 10,
                  color: isHovered ? Color(0xff0058FF) : Colors.black,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Sidebar item with sub-items
class _SidebarItemWithSubItems extends StatefulWidget {
  final String iconPath;
  final String label;
  final List<dynamic> subItems;
  final VoidCallback onTap;
  final Function(Map<String, dynamic>) onSubItemTap;
  final bool isSelected;

  const _SidebarItemWithSubItems({
    required this.iconPath,
    required this.label,
    required this.subItems,
    required this.onTap,
    required this.onSubItemTap,
    this.isSelected = false,
  });

  @override
  State<_SidebarItemWithSubItems> createState() =>
      _SidebarItemWithSubItemsState();
}

class _SidebarItemWithSubItemsState extends State<_SidebarItemWithSubItems> {
  bool isHovered = false;
  bool isExpanded = false;
  final GlobalKey _iconKey = GlobalKey();
  OverlayEntry? _overlayEntry;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    try {
      _removeOverlay();

      // Get the position of the icon
      final RenderBox? renderBox =
          _iconKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox == null) return;

      final position = renderBox.localToGlobal(Offset.zero);
      final size = renderBox.size;

      _overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            // For web: Invisible bridge between icon and menu to prevent closing when moving mouse
            if (kIsWeb)
              Positioned(
                left: position.dx + size.width - 5,
                top: position.dy,
                width: 10, // Small bridge
                height: size.height,
                child: MouseRegion(
                  opaque: false, // Make it non-blocking for mouse events
                ),
              ),
            // For mobile/tablet: Add a tap-to-close overlay
            if (!kIsWeb)
              Positioned.fill(
                child: GestureDetector(
                  onTap: () {
                    // Close overlay when tapping outside
                    _removeOverlay();
                  },
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
              ),
            // Actual menu
            Positioned(
              left: position.dx + size.width,
              top: position.dy, // Align exactly with the icon
              child: kIsWeb
                  ? MouseRegion(
                      onExit: (_) {
                        // Remove overlay when mouse exits the overlay (web only)
                        _removeOverlay();
                      },
                      child: _buildSubmenuContent(),
                    )
                  : _buildSubmenuContent(),
            ),
          ],
        ),
      );

      // Check if the context is still valid before inserting the overlay
      if (!mounted) return;

      // Store the overlay in the global variable
      activeBusinessSubmenu = _overlayEntry;

      // Insert the overlay
      Overlay.of(context).insert(_overlayEntry!);
    } catch (e) {
      // Safely handle any errors that might occur when showing the overlay
      print('Error showing overlay: $e');
      _overlayEntry = null;
      setState(() {
        isExpanded = false;
      });
    }
  }

  Widget _buildSubmenuContent() {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: 90, // Reduced width for single column layout
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Color(0xffD0D0D0), width: 1),
          boxShadow: !kIsWeb
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 6,
                    offset: Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Title
            Container(
              width: double.infinity,
              height: 35, // Match the height of the sidebar icon container
              decoration: BoxDecoration(
                color: Colors.black, // Blue background
                border: Border(
                  bottom: BorderSide(color: Color(0xffD0D0D0), width: 1),
                ),
              ),
              child: Center(
                child: Text(
                  widget.label,
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: Colors.white, // White text
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            // Sub-items in a single column
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: widget.subItems
                    .map((subItem) => _buildSubItem(
                          subItem['icon'],
                          subItem['label'],
                          () {
                            // Handle sub-item tap
                            _removeOverlay();
                            // Navigate based on the sub-item
                            widget.onSubItemTap(subItem);
                          },
                        ))
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _removeOverlay() {
    try {
      if (_overlayEntry != null) {
        _overlayEntry!.remove();
        _overlayEntry = null;
        setState(() {
          isExpanded = false;
        });

        // Clear the global variable if this is the active overlay
        if (activeBusinessSubmenu == _overlayEntry) {
          activeBusinessSubmenu = null;
        }
      }
    } catch (e) {
      // Safely handle any errors that might occur when removing the overlay
      print('Error removing overlay: $e');
      _overlayEntry = null;
      setState(() {
        isExpanded = false;
      });

      // Always clear the global variable to prevent memory leaks
      if (activeBusinessSubmenu == _overlayEntry) {
        activeBusinessSubmenu = null;
      }
    }
  }

  void _handleTap() {
    if (kIsWeb) {
      // Web: Execute the main onTap action
      widget.onTap();
    } else {
      // Mobile/Tablet: Toggle submenu visibility
      if (isExpanded) {
        _removeOverlay();
      } else {
        _showOverlay();
        setState(() {
          isExpanded = true;
        });
      }
    }
  }

  Widget _buildSubItem(String iconPath, String label, VoidCallback onTap) {
    return _SubMenuItem(
      iconPath: iconPath,
      label: label,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xxs),
      child: kIsWeb
          ? MouseRegion(
              onEnter: (_) {
                setState(() => isHovered = true);
                // Show overlay on hover (web only)
                _showOverlay();
                setState(() {
                  isExpanded = true;
                });
              },
              onExit: (_) {
                setState(() => isHovered = false);
                // We'll handle overlay removal in MouseRegion of the overlay
              },
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: _handleTap,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
                  key: _iconKey,
                  decoration: BoxDecoration(
                    // Only apply hover effect when mouse is directly over the icon or when selected
                    color: (isHovered || widget.isSelected)
                        ? Colors.blue.withValues(alpha: 0.05)
                        : Colors.transparent,
                  ),
                  child: Center(
                    child: SvgPicture.asset(
                      'assets/images/my_library/bussiness_desktop.svg',

                      // Change SVG color only when directly hovered or selected
                      colorFilter: (isHovered || widget.isSelected)
                          ? ColorFilter.mode(Color(0xff0058FF), BlendMode.srcIn)
                          : null,
                    ),
                  ),
                ),
              ),
            )
          : GestureDetector(
              onTap: _handleTap,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
                key: _iconKey,
                decoration: BoxDecoration(
                  // Apply selection effect when expanded or selected
                  color: (isExpanded || widget.isSelected)
                      ? Colors.blue.withValues(alpha: 0.05)
                      : Colors.transparent,
                ),
                child: Center(
                  child: SvgPicture.asset(
                    widget.iconPath,
                    height: 20,
                    // Change SVG color when expanded or selected
                    colorFilter: (isExpanded || widget.isSelected)
                        ? ColorFilter.mode(Color(0xff0058FF), BlendMode.srcIn)
                        : null,
                  ),
                ),
              ),
            ),
    );
  }
}
