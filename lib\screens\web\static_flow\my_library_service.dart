import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart' hide Summary;
import 'get_all_my_library_model.dart';

class MyLibraryService {
  static const String _baseUrl = 'http://10.26.1.52:8102'; // Updated with your API base URL
  static const Duration _timeout = Duration(seconds: 30);
  
  // Headers for API requests
  static Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Add authorization token if needed
    // 'Authorization': 'Bearer $token',
  };

  // Helper method to parse JSON response
  static GetAllMylibrarylist? _parseGetAllMylibrarylist(Map<String, dynamic> json) {
    try {
      return GetAllMylibrarylist(
        success: json['success'],
        message: json['message'],
        data: json['data'] != null ? _parseData(json['data']) : null,
        error: json['error'],
        timestamp: json['timestamp'] != null ? DateTime.tryParse(json['timestamp'].toString()) : null,
      );
    } catch (e) {
      debugPrint('Error parsing GetAllMylibrarylist: $e');
      return null;
    }
  }

  // Helper method to parse Data
  static Data? _parseData(Map<String, dynamic> json) {
    try {
      return Data(
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
        dataLibrary: json['library'] != null ? _parseLibrary(json['library']) : null,
        summary: json['summary'] != null ? _parseSummary(json['summary']) : null,
        metadata: json['metadata'] != null ? _parseMetadata(json['metadata']) : null,
      );
    } catch (e) {
      debugPrint('Error parsing Data: $e');
      return null;
    }
  }

  // Helper method to parse TenantId
  static TenantId? _parseTenantId(dynamic value) {
    if (value == null) return null;
    String tenantIdStr = value.toString();
    switch (tenantIdStr) {
      case 'T1008':
        return TenantId.T1008;
      default:
        return TenantId.T1008; // Default fallback
    }
  }

  // Helper method to parse Library
  static Library? _parseLibrary(Map<String, dynamic> json) {
    try {
      return Library(
        roles: json['roles'] != null ? _parseRoles(json['roles']) : null,
        entities: json['entities'] != null ? _parseEntities(json['entities']) : null,
        entityAttributes: json['entity_attributes'] != null ? _parseEntityAttributes(json['entity_attributes']) : null,
        departments: json['departments'] != null ? _parseDepartments(json['departments']) : null,
        businessRules: json['business_rules'] != null ? _parseBusinessRules(json['business_rules']) : null,
        globalObjectives: json['global_objectives'] != null ? _parseGlobalObjectives(json['global_objectives']) : null,
        localObjectives: json['local_objectives'] != null ? _parseLocalObjectives(json['local_objectives']) : null,
        systemPermissions: json['system_permissions'] != null ? _parseSystemPermissions(json['system_permissions']) : null,
        securityProperties: json['security_properties'] != null ? _parseSecurityProperties(json['security_properties']) : null,
        entityRelationships: json['entity_relationships'] != null ? _parseEntityRelationships(json['entity_relationships']) : null,
        attributeValidations: json['attribute_validations'] != null ? _parseAttributeValidations(json['attribute_validations']) : null,
      );
    } catch (e) {
      debugPrint('Error parsing Library: $e');
      return null;
    }
  }

  // Helper method to parse Roles
  static Roles? _parseRoles(Map<String, dynamic> json) {
    try {
      return Roles(
        postgres: json['postgres'] != null ? 
          (json['postgres'] as List).map((e) => _parseRolesPostgre(e)).where((e) => e != null).cast<RolesPostgre>().toList() : null,
        mongoDrafts: json['mongo_drafts'] as List?,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing Roles: $e');
      return null;
    }
  }

  static RolesPostgre? _parseRolesPostgre(Map<String, dynamic> json) {
    try {
      return RolesPostgre(
        roleId: json['role_id'],
        name: json['name'],
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
        description: json['description'],
        createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
        updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
        version: json['version'],
        naturalLanguage: json['natural_language'],
      );
    } catch (e) {
      debugPrint('Error parsing RolesPostgre: $e');
      return null;
    }
  }

  // Helper method to parse Entities
  static Entities? _parseEntities(Map<String, dynamic> json) {
    try {
      return Entities(
        postgres: json['postgres'] != null ? 
          (json['postgres'] as List).map((e) => _parseEntitiesMongoDraft(e)).where((e) => e != null).cast<EntitiesMongoDraft>().toList() : null,
        mongoDrafts: json['mongo_drafts'] != null ? 
          (json['mongo_drafts'] as List).map((e) => _parseEntitiesMongoDraft(e)).where((e) => e != null).cast<EntitiesMongoDraft>().toList() : null,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing Entities: $e');
      return null;
    }
  }

  static EntitiesMongoDraft? _parseEntitiesMongoDraft(Map<String, dynamic> json) {
    try {
      return EntitiesMongoDraft(
        id: json['id'],
        entityId: json['entity_id'],
        name: json['name'],
        displayName: json['display_name'],
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
        description: json['description'],
        tableName: json['table_name'],
        createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
        updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
        version: json['version'],
        naturalLanguage: json['natural_language'],
        businessDomain: json['business_domain'],
        category: json['category'],
        tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      );
    } catch (e) {
      debugPrint('Error parsing EntitiesMongoDraft: $e');
      return null;
    }
  }

  // Helper method to parse EntityAttributes
  static EntityAttributes? _parseEntityAttributes(Map<String, dynamic> json) {
    try {
      return EntityAttributes(
        postgres: json['postgres'] != null ? 
          (json['postgres'] as List).map((e) => _parseEntityAttributesMongoDraft(e)).where((e) => e != null).cast<EntityAttributesMongoDraft>().toList() : null,
        mongoDrafts: json['mongo_drafts'] != null ? 
          (json['mongo_drafts'] as List).map((e) => _parseEntityAttributesMongoDraft(e)).where((e) => e != null).cast<EntityAttributesMongoDraft>().toList() : null,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing EntityAttributes: $e');
      return null;
    }
  }

  static EntityAttributesMongoDraft? _parseEntityAttributesMongoDraft(Map<String, dynamic> json) {
    try {
      return EntityAttributesMongoDraft(
        id: json['id'],
        attributeId: json['attribute_id'],
        entityId: json['entity_id'],
        name: json['name'],
        displayName: json['display_name'],
        description: json['description'],
        createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
        updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
        version: json['version'],
        naturalLanguage: json['natural_language'],
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
      );
    } catch (e) {
      debugPrint('Error parsing EntityAttributesMongoDraft: $e');
      return null;
    }
  }

  // Placeholder methods for other parsing functions
  static Departments? _parseDepartments(Map<String, dynamic> json) {
    try {
      return Departments(
        postgres: json['postgres'] != null ? 
          (json['postgres'] as List).map((e) => _parseDepartmentsPostgre(e)).where((e) => e != null).cast<DepartmentsPostgre>().toList() : null,
        mongoDrafts: json['mongo_drafts'] != null ? 
          (json['mongo_drafts'] as List).map((e) => _parsePostgresRecordElement(e)).where((e) => e != null).cast<PostgresRecordElement>().toList() : null,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing Departments: $e');
      return null;
    }
  }

  static DepartmentsPostgre? _parseDepartmentsPostgre(Map<String, dynamic> json) {
    try {
      return DepartmentsPostgre(
        departmentId: json['department_id'],
        name: json['name'],
        description: json['description'],
        createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
        updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
        naturalLanguage: json['natural_language'],
        version: json['version'],
      );
    } catch (e) {
      debugPrint('Error parsing DepartmentsPostgre: $e');
      return null;
    }
  }

  static PostgresRecordElement? _parsePostgresRecordElement(Map<String, dynamic> json) {
    try {
      return PostgresRecordElement(
        id: json['id'],
        name: json['name'],
        description: json['description'],
        createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
        updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
        naturalLanguage: json['natural_language'],
        version: json['version'],
      );
    } catch (e) {
      debugPrint('Error parsing PostgresRecordElement: $e');
      return null;
    }
  }

  static BusinessRules? _parseBusinessRules(Map<String, dynamic> json) => null;
  
  static GlobalObjectives? _parseGlobalObjectives(Map<String, dynamic> json) {
    try {
      return GlobalObjectives(
        postgres: json['postgres'] != null ? 
          (json['postgres'] as List).map((e) => _parseGlobalObjectivesPostgre(e)).where((e) => e != null).cast<GlobalObjectivesPostgre>().toList() : null,
        mongoDrafts: json['mongo_drafts'] as List?,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing GlobalObjectives: $e');
      return null;
    }
  }

  static GlobalObjectivesPostgre? _parseGlobalObjectivesPostgre(Map<String, dynamic> json) {
    try {
      return GlobalObjectivesPostgre(
        goId: json['go_id'],
        name: json['name'],
        description: json['description'],
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
        createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
        updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
        version: json['version'],
        naturalLanguage: json['natural_language'],
        primaryEntity: json['primary_entity'],
        classification: json['classification'],
        bookId: json['book_id'],
        bookName: json['book_name'],
        chapterId: json['chapter_id'],
        chapterName: json['chapter_name'],
      );
    } catch (e) {
      debugPrint('Error parsing GlobalObjectivesPostgre: $e');
      return null;
    }
  }
  
  static LocalObjectives? _parseLocalObjectives(Map<String, dynamic> json) {
    try {
      return LocalObjectives(
        postgres: json['postgres'] != null ? 
          (json['postgres'] as List).map((e) => _parseLocalObjectivesPostgre(e)).where((e) => e != null).cast<LocalObjectivesPostgre>().toList() : null,
        mongoDrafts: json['mongo_drafts'] as List?,
        totalPostgres: json['total_postgres'],
        totalDrafts: json['total_drafts'],
      );
    } catch (e) {
      debugPrint('Error parsing LocalObjectives: $e');
      return null;
    }
  }

  static LocalObjectivesPostgre? _parseLocalObjectivesPostgre(Map<String, dynamic> json) {
    try {
      return LocalObjectivesPostgre(
        loId: json['lo_id'],
        name: json['name'],
        functionType: json['function_type'],
        goId: json['go_id'],
        description: json['description'] != null ? Description.AUTO_GENERATED_FROM_GO_JSON : null,
        naturalLanguage: json['natural_language'],
        createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
        updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
        version: json['version'],
        tenantId: json['tenant_id'] != null ? _parseTenantId(json['tenant_id']) : null,
        workflowSource: json['workflow_source'],
        versionType: json['version_type'] != null ? VersionType.V2 : null,
        autoId: json['auto_id'],
        agentType: json['agent_type'] != null ? _parseAgentType(json['agent_type']) : null,
        executionRights: json['execution_rights'],
        terminal: json['terminal'],
        tableName: json['table_name'],
        goName: json['go_name'],
      );
    } catch (e) {
      debugPrint('Error parsing LocalObjectivesPostgre: $e');
      return null;
    }
  }

  static AgentType? _parseAgentType(String? value) {
    if (value == null) return null;
    switch (value.toUpperCase()) {
      case 'HUMAN':
        return AgentType.HUMAN;
      case 'SYSTEM':
        return AgentType.SYSTEM;
      default:
        return AgentType.HUMAN; // Default fallback
    }
  }
  static SystemPermissions? _parseSystemPermissions(Map<String, dynamic> json) => null;
  static SecurityProperties? _parseSecurityProperties(Map<String, dynamic> json) => null;
  static EntityRelationships? _parseEntityRelationships(Map<String, dynamic> json) => null;
  static AttributeValidations? _parseAttributeValidations(Map<String, dynamic> json) => null;
  
  static Summary? _parseSummary(Map<String, dynamic> json) {
    try {
      return Summary(
        totalItemsPostgres: json['total_items_postgres'],
        totalItemsMongo: json['total_items_mongo'],
        totalItemsCombined: json['total_items_combined'],
        tenantExistsPostgres: json['tenant_exists_postgres'],
        tenantExistsMongo: json['tenant_exists_mongo'],
      );
    } catch (e) {
      debugPrint('Error parsing Summary: $e');
      return null;
    }
  }
  
  static Metadata? _parseMetadata(Map<String, dynamic> json) {
    try {
      return Metadata(
        retrievedAt: json['retrieved_at'] != null ? DateTime.tryParse(json['retrieved_at'].toString()) : null,
        dataSources: json['data_sources'] != null ? List<String>.from(json['data_sources']) : null,
        categoriesIncluded: json['categories_included'] != null ? List<String>.from(json['categories_included']) : null,
      );
    } catch (e) {
      debugPrint('Error parsing Metadata: $e');
      return null;
    }
  }

  // Get all library data
  static Future<GetAllMylibrarylist?> getAllLibraryData({
    required String tenantId,
    String? token,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl/library/$tenantId');
      
      final headers = Map<String, String>.from(_headers);
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      final response = await http.get(
        uri,
        headers: headers,
      ).timeout(_timeout);

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        return _parseGetAllMylibrarylist(jsonData);
      } else {
        debugPrint('Failed to load library data: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error getting library data: $e');
      return null;
    }
  }

  // Get entities
  static Future<Entities?> getEntities({
    required String tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData(
        tenantId: tenantId,
        token: token,
      );
      
      return response?.data?.dataLibrary?.entities;
    } catch (e) {
      debugPrint('Error getting entities: $e');
      return null;
    }
  }

  // Get roles
  static Future<Roles?> getRoles({
    required String tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData(
        tenantId: tenantId,
        token: token,
      );
      
      return response?.data?.dataLibrary?.roles;
    } catch (e) {
      debugPrint('Error getting roles: $e');
      return null;
    }
  }

  // Get entity attributes
  static Future<EntityAttributes?> getEntityAttributes({
    required String tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData(
        tenantId: tenantId,
        token: token,
      );
      
      return response?.data?.dataLibrary?.entityAttributes;
    } catch (e) {
      debugPrint('Error getting entity attributes: $e');
      return null;
    }
  }

  // Get attributes for a specific entity
  static Future<List<EntityAttributesMongoDraft>?> getAttributesForEntity({
    required String tenantId,
    required String entityId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData(
        tenantId: tenantId,
        token: token,
      );
      
      final allAttributes = <EntityAttributesMongoDraft>[];
      
      // Add postgres attributes
      if (response?.data?.dataLibrary?.entityAttributes?.postgres != null) {
        allAttributes.addAll(response!.data!.dataLibrary!.entityAttributes!.postgres!);
      }
      
      // Add mongo draft attributes
      if (response?.data?.dataLibrary?.entityAttributes?.mongoDrafts != null) {
        allAttributes.addAll(response!.data!.dataLibrary!.entityAttributes!.mongoDrafts!);
      }
      
      return allAttributes.where((attr) => attr.entityId == entityId).toList();
    } catch (e) {
      debugPrint('Error getting attributes for entity: $e');
      return null;
    }
  }

  // Search entities by name
  static Future<List<EntitiesMongoDraft>?> searchEntities({
    required String tenantId,
    required String searchQuery,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData(
        tenantId: tenantId,
        token: token,
      );
      
      final allEntities = <EntitiesMongoDraft>[];
      
      // Add postgres entities
      if (response?.data?.dataLibrary?.entities?.postgres != null) {
        allEntities.addAll(response!.data!.dataLibrary!.entities!.postgres!);
      }
      
      // Add mongo draft entities
      if (response?.data?.dataLibrary?.entities?.mongoDrafts != null) {
        allEntities.addAll(response!.data!.dataLibrary!.entities!.mongoDrafts!);
      }
      
      return allEntities.where((entity) =>
        (entity.name?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
        (entity.displayName?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false)
      ).toList();
    } catch (e) {
      debugPrint('Error searching entities: $e');
      return null;
    }
  }

  // Check if tenant exists
  static Future<bool> checkTenantExists({
    required String tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData(
        tenantId: tenantId,
        token: token,
      );
      
      final summary = response?.data?.summary;
      return (summary?.tenantExistsPostgres ?? false) ||
             (summary?.tenantExistsMongo ?? false);
    } catch (e) {
      debugPrint('Error checking tenant exists: $e');
      return false;
    }
  }

  // Get roles list for display
  static Future<List<String>?> getRolesList({
    required String tenantId,
    String? token,
  }) async {
    try {
      final rolesResponse = await getRoles(
        tenantId: tenantId,
        token: token,
      );
      
      final rolesList = <String>[];
      
      // Add postgres roles
      if (rolesResponse?.postgres != null) {
        for (final role in rolesResponse!.postgres!) {
          if (role.name != null) {
            rolesList.add(role.name!);
          }
        }
      }
      
      return rolesList.isNotEmpty ? rolesList : null;
    } catch (e) {
      debugPrint('Error getting roles list: $e');
      return null;
    }
  }

  // Get entities list for display
  static Future<List<Map<String, dynamic>>?> getEntitiesList({
    required String tenantId,
    String? token,
  }) async {
    try {
      final entitiesResponse = await getEntities(
        tenantId: tenantId,
        token: token,
      );
      
      final entitiesList = <Map<String, dynamic>>[];
      
      // Add postgres entities
      if (entitiesResponse?.postgres != null) {
        for (final entity in entitiesResponse!.postgres!) {
          entitiesList.add({
            'entityId': entity.entityId ?? '',
            'name': entity.name ?? 'Unknown Entity',
            'displayName': entity.displayName ?? entity.name ?? 'Unknown Entity',
            'attributes': <String>[],
            'isExpanded': false,
            'attributesLoaded': false,
          });
        }
      }
      
      // Add mongo draft entities
      if (entitiesResponse?.mongoDrafts != null) {
        for (final entity in entitiesResponse!.mongoDrafts!) {
          entitiesList.add({
            'entityId': entity.entityId ?? '',
            'name': entity.name ?? 'Unknown Entity',
            'displayName': entity.displayName ?? entity.name ?? 'Unknown Entity',
            'attributes': <String>[],
            'isExpanded': false,
            'attributesLoaded': false,
          });
        }
      }
      
      return entitiesList.isNotEmpty ? entitiesList : null;
    } catch (e) {
      debugPrint('Error getting entities list: $e');
      return null;
    }
  }

  // Get attributes list for a specific entity
  static Future<List<String>?> getAttributesListForEntity({
    required String tenantId,
    required String entityId,
    String? token,
  }) async {
    try {
      final attributesResponse = await getAttributesForEntity(
        tenantId: tenantId,
        entityId: entityId,
        token: token,
      );
      
      if (attributesResponse != null) {
        return attributesResponse
            .map((attr) => attr.displayName?.isNotEmpty == true 
                ? attr.displayName! 
                : attr.name ?? 'Unknown Attribute')
            .toList();
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting attributes list for entity $entityId: $e');
      return null;
    }
  }

  // Get global objectives
  static Future<GlobalObjectives?> getGlobalObjectives({
    required String tenantId,
    String? token,
  }) async {
    try {
      final response = await getAllLibraryData(
        tenantId: tenantId,
        token: token,
      );
      
      return response?.data?.dataLibrary?.globalObjectives;
    } catch (e) {
      debugPrint('Error getting global objectives: $e');
      return null;
    }
  }

  // Get global objectives list for display
  static Future<List<String>?> getGlobalObjectivesList({
    required String tenantId,
    String? token,
  }) async {
    try {
      final globalObjectivesResponse = await getGlobalObjectives(
        tenantId: tenantId,
        token: token,
      );
      
      final goList = <String>[];
      
      // Add postgres global objectives
      if (globalObjectivesResponse?.postgres != null) {
        for (final go in globalObjectivesResponse!.postgres!) {
          if (go.name != null) {
            goList.add(go.name!);
          }
        }
      }
      
      return goList.isNotEmpty ? goList : null;
    } catch (e) {
      debugPrint('Error getting global objectives list: $e');
      return null;
    }
  }
}
