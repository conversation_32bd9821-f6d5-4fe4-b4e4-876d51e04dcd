import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDrawerItem extends StatefulWidget {
  final String icon;
  final String title;
  final VoidCallback? onTap;
  final List<Widget>? children;
  final bool useImage; // set true if using Image.asset instead of SVG
  final double? iconWidth;
  final double? iconHeight;

  const CustomDrawerItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.children,
    this.useImage = false,
    this.iconWidth,
    this.iconHeight,
  });

  @override
  State<CustomDrawerItem> createState() => _CustomDrawerItemState();
}

class _CustomDrawerItemState extends State<CustomDrawerItem> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    // Standardize icon size to 20x20 for consistency

    // Standardized icon container with consistent 20x20 size
    Widget iconContainer = SizedBox(
      child: widget.useImage
          ? Image.asset(
              widget.icon,
              fit: BoxFit.contain,
            )
          : SvgPicture.asset(
              widget.icon,
              fit: BoxFit.contain,
              colorFilter: const ColorFilter.mode(
                Colors.black87,
                BlendMode.srcIn,
              ),
            ),
    );

    if (widget.children != null && widget.children!.isNotEmpty) {
      return Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 2),
          child: ExpansionTile(
            tilePadding: EdgeInsets.zero,
            childrenPadding: const EdgeInsets.only(
                left: 48), // 20 (icon) + 8 (gap) + 20 (padding) = 48
            leading: null,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                iconContainer,
                const SizedBox(width: 8), // Consistent 8px gap
                Expanded(
                  child: Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      fontFamily: 'inter',
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
            trailing: AnimatedRotation(
              turns: _isExpanded ? 0.5 : 0.0,
              duration: const Duration(milliseconds: 200),
              child: SizedBox(
                width: 32,
                height: 32,
                child: SvgPicture.asset(
                  'assets/images/sidebar_icons/down.svg',
                  fit: BoxFit.contain,
                  colorFilter: const ColorFilter.mode(
                    Colors.black54,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
            onExpansionChanged: (bool expanded) {
              setState(() {
                _isExpanded = expanded;
              });
            },
            minTileHeight: 35,
            shape: const Border(),
            collapsedShape: const Border(),
            children: widget.children!,
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 2),
      child: InkWell(
        onTap: widget.onTap,
        child: Container(
          height: 35,
          child: Row(
            children: [
              iconContainer,
              const SizedBox(width: 8), // Consistent 8px gap
              Expanded(
                child: Text(
                  widget.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'inter',
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
