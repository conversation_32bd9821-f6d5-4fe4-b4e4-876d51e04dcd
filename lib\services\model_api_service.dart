import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:nsl/config/environment.dart';
import '../utils/logger.dart';
import '../l10n/app_localizations.dart';

class ModelApiService {
  final Dio _dio = Dio();

  // API endpoint



  // Singleton instance
  static final ModelApiService _instance = ModelApiService._internal();

  // Factory constructor
  factory ModelApiService() => _instance;

  // Internal constructor
  ModelApiService._internal();

  /// Fetch available models from the API
  Future<Map<String, dynamic>> fetchModels() async {
    try {
      Logger.info('Fetching models from API: $Environment.modelsApiUrl');

      final response = await _dio.get(
        Environment.modelsApiUrl,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Models API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        Logger.error('Models API failed with status: ${response.statusCode}');
        return {
          'success': false,
          'message': 'Failed to fetch models: ${response.statusCode}',
        };
      }
    } catch (e) {
      Logger.error('Error fetching models: $e');
      return {
        'success': false,
        'message': 'Failed to fetch models: $e',
      };
    }
  }

  /// Transform API response to ModelInfo list
  List<ModelInfo> transformApiResponseToModels(
      Map<String, dynamic> apiResponse) {
    try {
      final availableModels =
          apiResponse['available_models'] as Map<String, dynamic>?;
      final fallbackOrder = apiResponse['fallback_order'] as List<dynamic>?;
      final recommendations =
          apiResponse['recommendations'] as Map<String, dynamic>?;

      if (availableModels == null || fallbackOrder == null) {
        Logger.error('Invalid API response structure');
        return [];
      }

      List<ModelInfo> models = [];

      // Use fallback_order to determine GLM numbering
      for (int i = 0; i < fallbackOrder.length; i++) {
        final modelKey = fallbackOrder[i] as String;
        final modelData = availableModels[modelKey] as Map<String, dynamic>?;

        if (modelData != null && modelData['available'] == true) {
          // Generate GLM version number (1.0, 1.1, 1.2, etc.)
          final glmVersion = '1.$i';

          // Capitalize model name for display
          final displayName = _capitalizeModelName(modelKey);

          // Generate description based on recommended_for
          final recommendedFor =
              modelData['recommended_for'] as List<dynamic>? ?? [];
          final description = _generateDescription(recommendedFor);

          // Check if this should be selected (Claude is default)
          final isSelected = modelKey == 'claude';

          models.add(ModelInfo(
            id: 'GLM $glmVersion',
            // name: 'GLM $glmVersion ($displayName)',
            name: 'GLM $glmVersion',
            description: description,
            apiKey: modelKey,
            isSelected: isSelected,
          ));
        }
      }

      Logger.info('Transformed ${models.length} models from API response');
      return models;
    } catch (e) {
      Logger.error('Error transforming API response: $e');
      return [];
    }
  }

  /// Capitalize model name for display using toTitleCase extension
  String _capitalizeModelName(String modelKey) {
    // Use the existing toTitleCase extension which handles underscores and capitalization
    return modelKey.replaceAll('_', ' ').toTitleCase();
  }

  /// Generate description based on recommended_for array
  String _generateDescription(List<dynamic> recommendedFor) {
    if (recommendedFor.isEmpty) {
      return 'Versatile model for general use';
    }

    final recommendations = recommendedFor.cast<String>();

    if (recommendations.contains('start') &&
        recommendations.contains('expand')) {
      return 'Powerful model for complex challenges';
    } else if (recommendations.contains('continue')) {
      return 'Efficient model for ongoing tasks';
    } else if (recommendations.contains('generate')) {
      return 'Creative model for content generation';
    } else if (recommendations.contains('start')) {
      return 'Ideal model for starting new projects';
    } else {
      return 'Smart model for ${recommendations.join(', ')} tasks';
    }
  }
}

/// Enhanced ModelInfo class with API key
class ModelInfo {
  final String id;
  final String name;
  final String description;
  final String apiKey; // Original API key (claude, qwen_local, etc.)
  final bool isPro;
  final bool isSelected;

  ModelInfo({
    required this.id,
    required this.name,
    required this.description,
    required this.apiKey,
    this.isPro = false,
    this.isSelected = false,
  });
}
